#!/usr/bin/env python3
"""
Test script for lightweight VSR backend integration
Tests the /predict_v2 endpoint with sample videos
"""

import requests
import os
import sys
import time
import json
from pathlib import Path
from typing import Dict, Any

# Configuration
BACKEND_URL = "http://localhost:8000"
TEST_VIDEO_DIR = "test_videos"

def test_health_endpoint():
    """Test the health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"   VSR Implementation: {data.get('vsr_impl', 'unknown')}")
            print(f"   Lightweight Available: {data.get('lightweight_available', False)}")
            print(f"   Model Path: {data.get('prototype_model', 'not specified')}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_predict_v2_endpoint(video_path: str) -> Dict[str, Any]:
    """Test the /predict_v2 endpoint with a video file"""
    print(f"🔍 Testing /predict_v2 with {video_path}...")
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return {"error": "File not found"}
    
    try:
        with open(video_path, 'rb') as f:
            files = {'file': (os.path.basename(video_path), f, 'video/mp4')}
            
            start_time = time.time()
            response = requests.post(
                f"{BACKEND_URL}/predict_v2",
                files=files,
                timeout=30
            )
            inference_time = time.time() - start_time
            
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Prediction successful")
            print(f"   Phrase: {result.get('phrase', 'unknown')}")
            print(f"   Confidence: {result.get('confidence', 0):.3f}")
            print(f"   Inference Time: {inference_time:.2f}s")
            
            # Add timing info
            result['total_time_seconds'] = inference_time
            return result
        else:
            print(f"❌ Prediction failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return {"error": f"HTTP {response.status_code}: {response.text}"}
            
    except Exception as e:
        print(f"❌ Prediction error: {e}")
        return {"error": str(e)}

def find_test_videos() -> list:
    """Find available test videos"""
    test_videos = []
    
    # Look for videos in common locations
    search_paths = [
        "test_videos",
        "sample_videos", 
        "LipreadingApp/test_videos",
        "/Users/<USER>/Desktop/entire dataset 23.8.25/YES top 10"
    ]
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.lower().endswith(('.mp4', '.webm', '.avi', '.mov')):
                        video_path = os.path.join(root, file)
                        test_videos.append(video_path)
                        if len(test_videos) >= 5:  # Limit to 5 videos for testing
                            break
                if len(test_videos) >= 5:
                    break
    
    return test_videos

def run_comprehensive_test():
    """Run comprehensive backend test"""
    print("🚀 Starting Lightweight VSR Backend Test")
    print("=" * 50)
    
    # Test 1: Health check
    if not test_health_endpoint():
        print("❌ Backend not ready. Make sure the server is running with VSR_IMPL=lightweight")
        return False
    
    print()
    
    # Test 2: Find test videos
    test_videos = find_test_videos()
    if not test_videos:
        print("❌ No test videos found. Please add some video files to test with.")
        return False
    
    print(f"📹 Found {len(test_videos)} test videos")
    
    # Test 3: Run predictions
    results = []
    for i, video_path in enumerate(test_videos[:3]):  # Test first 3 videos
        print(f"\n--- Test {i+1}/3 ---")
        result = test_predict_v2_endpoint(video_path)
        results.append({
            'video': os.path.basename(video_path),
            'result': result
        })
    
    # Test 4: Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    successful_tests = 0
    total_time = 0
    
    for test in results:
        video = test['video']
        result = test['result']
        
        if 'error' not in result:
            successful_tests += 1
            total_time += result.get('total_time_seconds', 0)
            print(f"✅ {video}: {result.get('phrase', 'unknown')} ({result.get('confidence', 0):.3f})")
        else:
            print(f"❌ {video}: {result['error']}")
    
    print(f"\nSuccess Rate: {successful_tests}/{len(results)} ({successful_tests/len(results)*100:.1f}%)")
    if successful_tests > 0:
        avg_time = total_time / successful_tests
        print(f"Average Inference Time: {avg_time:.2f}s")
    
    return successful_tests > 0

def test_mobile_app_integration():
    """Test mobile app integration endpoints"""
    print("\n🔍 Testing Mobile App Integration")
    print("-" * 30)
    
    # Test status endpoint
    try:
        response = requests.get(f"{BACKEND_URL}/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Status endpoint working")
            print(f"   Available endpoints: {len(data.get('endpoints', {}))}")
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Status endpoint error: {e}")

if __name__ == "__main__":
    # Check if backend URL is provided
    if len(sys.argv) > 1:
        BACKEND_URL = sys.argv[1]
    
    print(f"Testing backend at: {BACKEND_URL}")
    
    # Set environment variable for testing
    os.environ['VSR_IMPL'] = 'lightweight'
    
    # Run tests
    success = run_comprehensive_test()
    test_mobile_app_integration()
    
    if success:
        print("\n🎉 All tests passed! Backend is ready for mobile app integration.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the backend configuration.")
        sys.exit(1)
