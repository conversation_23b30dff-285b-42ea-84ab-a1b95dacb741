#!/usr/bin/env python3
"""
Simple backend test to verify the lightweight VSR system works
"""

import os
import sys
import requests
import time
import subprocess

def test_backend():
    print("🧪 Testing Lightweight VSR Backend")
    print("=" * 50)
    
    # Set environment for lightweight mode
    os.environ['VSR_IMPL'] = 'lightweight'
    
    # Start the backend server
    print("🚀 Starting backend server...")
    try:
        # Try to start with uvicorn directly
        proc = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "backend.api.app:app", 
            "--host", "127.0.0.1", 
            "--port", "8001"
        ], 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        env=os.environ.copy()
        )
        
        # Wait for server to start
        print("⏳ Waiting for server to start...")
        time.sleep(10)
        
        # Test health endpoint
        print("🔍 Testing health endpoint...")
        try:
            response = requests.get("http://127.0.0.1:8001/health", timeout=5)
            print(f"Health check status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check passed!")
                print(f"   VSR Implementation: {data.get('vsr_impl', 'unknown')}")
                print(f"   Status: {data.get('status', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False
        
    finally:
        # Clean up
        try:
            proc.terminate()
            proc.wait(timeout=5)
        except:
            proc.kill()

if __name__ == "__main__":
    success = test_backend()
    print("\n" + "=" * 50)
    if success:
        print("🎉 Backend test PASSED!")
    else:
        print("❌ Backend test FAILED!")
    sys.exit(0 if success else 1)
