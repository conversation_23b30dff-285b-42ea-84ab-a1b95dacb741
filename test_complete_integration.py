#!/usr/bin/env python3
"""
Complete Integration Test for Lightweight VSR System
Tests the entire pipeline from model training to mobile app integration
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path
from typing import Dict, Any, List

# Configuration
BACKEND_URL = "http://localhost:8000"
MODEL_DIR = "artifacts/prototype_10p_v1"
EXPECTED_PHRASES = [
    "doctor", "glasses", "help", "i_m_hot", "i_need_to_move",
    "my_back_hurts", "my_chest_hurts", "my_mouth_is_dry", "phone", "pillow"
]

class IntegrationTester:
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def test_model_artifacts(self) -> bool:
        """Test that model training produced the expected artifacts"""
        self.log("🔍 Testing model artifacts...")
        
        required_files = [
            "best_model.pth",
            "config.yaml", 
            "training_log.json",
            "vocab.json"
        ]
        
        if not os.path.exists(MODEL_DIR):
            self.log(f"❌ Model directory not found: {MODEL_DIR}", "ERROR")
            return False
            
        missing_files = []
        for file in required_files:
            file_path = os.path.join(MODEL_DIR, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
                
        if missing_files:
            self.log(f"❌ Missing model files: {missing_files}", "ERROR")
            return False
            
        # Check vocab contains expected phrases
        vocab_path = os.path.join(MODEL_DIR, "vocab.json")
        try:
            with open(vocab_path, 'r') as f:
                vocab = json.load(f)
                
            # Check if vocab contains our expected phrases
            vocab_words = set()
            for phrase in EXPECTED_PHRASES:
                # Convert phrase format (e.g., "i_m_hot" -> "i'm hot")
                readable_phrase = phrase.replace("_", " ").replace(" m ", "'m ")
                vocab_words.add(readable_phrase)
                
            self.log(f"✅ Model artifacts verified")
            self.log(f"   Vocab size: {len(vocab)}")
            self.log(f"   Expected phrases: {len(EXPECTED_PHRASES)}")
            return True
            
        except Exception as e:
            self.log(f"❌ Error reading vocab: {e}", "ERROR")
            return False
    
    def test_backend_startup(self) -> bool:
        """Test that backend starts correctly with lightweight model"""
        self.log("🔍 Testing backend startup...")
        
        # Set environment for lightweight mode
        env = os.environ.copy()
        env['VSR_IMPL'] = 'lightweight'
        
        try:
            # Test health endpoint
            response = requests.get(f"{BACKEND_URL}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log("✅ Backend health check passed")
                self.log(f"   VSR Implementation: {data.get('vsr_impl', 'unknown')}")
                self.log(f"   Lightweight Available: {data.get('lightweight_available', False)}")
                return True
            else:
                self.log(f"❌ Health check failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Backend connection error: {e}", "ERROR")
            return False
    
    def test_inference_performance(self) -> bool:
        """Test inference performance with sample videos"""
        self.log("🔍 Testing inference performance...")
        
        # Find test videos
        test_videos = self.find_test_videos()
        if not test_videos:
            self.log("❌ No test videos found", "ERROR")
            return False
            
        performance_results = []
        
        for i, video_path in enumerate(test_videos[:3]):  # Test first 3 videos
            self.log(f"   Testing video {i+1}/3: {os.path.basename(video_path)}")
            
            try:
                start_time = time.time()
                
                with open(video_path, 'rb') as f:
                    files = {'file': (os.path.basename(video_path), f, 'video/mp4')}
                    response = requests.post(
                        f"{BACKEND_URL}/predict_v2",
                        files=files,
                        timeout=30
                    )
                
                inference_time = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    performance_results.append({
                        'video': os.path.basename(video_path),
                        'inference_time': inference_time,
                        'phrase': result.get('phrase', 'unknown'),
                        'confidence': result.get('confidence', 0)
                    })
                    self.log(f"     ✅ {result.get('phrase', 'unknown')} ({result.get('confidence', 0):.3f}) - {inference_time:.2f}s")
                else:
                    self.log(f"     ❌ Failed: {response.status_code}", "ERROR")
                    
            except Exception as e:
                self.log(f"     ❌ Error: {e}", "ERROR")
        
        if performance_results:
            avg_time = sum(r['inference_time'] for r in performance_results) / len(performance_results)
            self.log(f"✅ Inference performance test completed")
            self.log(f"   Average inference time: {avg_time:.2f}s")
            self.log(f"   Successful predictions: {len(performance_results)}/3")
            
            # Store results for reporting
            self.results['inference_performance'] = {
                'average_time': avg_time,
                'successful_predictions': len(performance_results),
                'total_tests': 3,
                'details': performance_results
            }
            return True
        else:
            self.log("❌ No successful predictions", "ERROR")
            return False
    
    def test_mobile_app_compatibility(self) -> bool:
        """Test mobile app API compatibility"""
        self.log("🔍 Testing mobile app API compatibility...")
        
        try:
            # Test status endpoint
            response = requests.get(f"{BACKEND_URL}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                endpoints = data.get('endpoints', {})
                
                # Check required endpoints exist
                required_endpoints = ['/health', '/predict_v2', '/status']
                missing_endpoints = [ep for ep in required_endpoints if ep not in str(endpoints)]
                
                if missing_endpoints:
                    self.log(f"❌ Missing endpoints: {missing_endpoints}", "ERROR")
                    return False
                
                self.log("✅ Mobile app API compatibility verified")
                self.log(f"   Available endpoints: {len(endpoints)}")
                return True
            else:
                self.log(f"❌ Status endpoint failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ API compatibility test error: {e}", "ERROR")
            return False
    
    def find_test_videos(self) -> List[str]:
        """Find available test videos"""
        test_videos = []
        search_paths = [
            "test_videos",
            "sample_videos",
            "LipreadingApp/test_videos",
            "/Users/<USER>/Desktop/entire dataset 23.8.25/YES top 10"
        ]
        
        for search_path in search_paths:
            if os.path.exists(search_path):
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file.lower().endswith(('.mp4', '.webm', '.avi', '.mov')):
                            video_path = os.path.join(root, file)
                            test_videos.append(video_path)
                            if len(test_videos) >= 5:
                                break
                    if len(test_videos) >= 5:
                        break
        
        return test_videos
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        report = {
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_test_time': f"{total_time:.2f}s",
            'model_directory': MODEL_DIR,
            'backend_url': BACKEND_URL,
            'expected_phrases': EXPECTED_PHRASES,
            'results': self.results,
            'summary': {
                'tests_passed': len([k for k, v in self.results.items() if v.get('passed', False)]),
                'total_tests': len(self.results),
                'success_rate': 0
            }
        }
        
        if report['summary']['total_tests'] > 0:
            report['summary']['success_rate'] = (
                report['summary']['tests_passed'] / report['summary']['total_tests'] * 100
            )
        
        return report
    
    def run_complete_test(self) -> bool:
        """Run complete integration test suite"""
        self.log("🚀 Starting Complete Integration Test")
        self.log("=" * 60)
        
        tests = [
            ("Model Artifacts", self.test_model_artifacts),
            ("Backend Startup", self.test_backend_startup),
            ("Inference Performance", self.test_inference_performance),
            ("Mobile App Compatibility", self.test_mobile_app_compatibility)
        ]
        
        passed_tests = 0
        
        for test_name, test_func in tests:
            self.log(f"\n--- {test_name} ---")
            try:
                result = test_func()
                self.results[test_name.lower().replace(' ', '_')] = {'passed': result}
                if result:
                    passed_tests += 1
                    self.log(f"✅ {test_name} PASSED")
                else:
                    self.log(f"❌ {test_name} FAILED")
            except Exception as e:
                self.log(f"❌ {test_name} ERROR: {e}", "ERROR")
                self.results[test_name.lower().replace(' ', '_')] = {'passed': False, 'error': str(e)}
        
        # Generate final report
        self.log("\n" + "=" * 60)
        self.log("📊 Integration Test Summary")
        self.log("=" * 60)
        
        success_rate = (passed_tests / len(tests)) * 100
        self.log(f"Tests Passed: {passed_tests}/{len(tests)} ({success_rate:.1f}%)")
        
        if passed_tests == len(tests):
            self.log("🎉 ALL TESTS PASSED! System ready for production.")
            return True
        else:
            self.log(f"⚠️  {len(tests) - passed_tests} test(s) failed. Review issues above.")
            return False

if __name__ == "__main__":
    # Check if backend URL is provided
    if len(sys.argv) > 1:
        BACKEND_URL = sys.argv[1]
    
    # Set environment for lightweight mode
    os.environ['VSR_IMPL'] = 'lightweight'
    
    # Run integration tests
    tester = IntegrationTester()
    success = tester.run_complete_test()
    
    # Save detailed report
    report = tester.generate_report()
    report_path = "integration_test_report.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {report_path}")
    
    sys.exit(0 if success else 1)
