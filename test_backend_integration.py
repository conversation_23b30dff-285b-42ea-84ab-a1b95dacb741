#!/usr/bin/env python3
"""
Test script for backend integration with prototype model
"""

import os
import sys
import requests
import json
from pathlib import Path

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   VSR Implementation: {data.get('vsr_impl')}")
            print(f"   Lightweight Available: {data.get('lightweight_available')}")
            if 'prototype_model' in data:
                print(f"   Prototype Model: {data.get('prototype_model')}")
                print(f"   Model Type: {data.get('model_type')}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_predict_endpoint(video_path=None):
    """Test the predict_v2 endpoint"""
    if not video_path:
        print("⚠️  No video file provided for prediction test")
        return False
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        return False
    
    try:
        with open(video_path, 'rb') as f:
            files = {'file': f}
            response = requests.post("http://localhost:8000/predict_v2", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Prediction endpoint working")
            print(f"   Predicted phrase: {data.get('phrase')}")
            print(f"   Confidence: {data.get('confidence', 0):.3f}")
            print(f"   Inference time: {data.get('inference_time_ms', 0):.1f}ms")
            return True
        else:
            print(f"❌ Prediction endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Prediction endpoint error: {e}")
        return False

def check_model_files():
    """Check if model files exist"""
    model_path = "artifacts/prototype_10p_v1/best.ckpt"
    config_path = "configs/prototype_10_phrases.yaml"
    
    model_exists = os.path.exists(model_path)
    config_exists = os.path.exists(config_path)
    
    print(f"Model file ({model_path}): {'✅' if model_exists else '❌'}")
    print(f"Config file ({config_path}): {'✅' if config_exists else '❌'}")
    
    return model_exists and config_exists

def start_backend():
    """Instructions to start the backend"""
    print("\n=== Backend Startup Instructions ===")
    print("1. Set environment variable:")
    print("   export VSR_IMPL=lightweight")
    print("\n2. Start the backend server:")
    print("   uvicorn backend.api.app:app --reload --host 0.0.0.0 --port 8000")
    print("\n3. Wait for server to start, then run this test again")

def main():
    print("=== Backend Integration Test ===")
    print("Testing prototype model integration\n")
    
    # Check if model files exist
    print("1. Checking model files...")
    if not check_model_files():
        print("\n❌ Model files not found. Training may still be in progress.")
        print("   Wait for training to complete, then run this test again.")
        return False
    
    # Test health endpoint
    print("\n2. Testing health endpoint...")
    if not test_health_endpoint():
        start_backend()
        return False
    
    # Find a sample video for testing
    print("\n3. Looking for sample video...")
    sample_video = None
    
    # Try to find a video from our dataset
    dataset_path = os.path.expanduser("~/Desktop/entire dataset 23.8.25/YES top 10")
    if os.path.exists(dataset_path):
        for phrase in ["doctor", "help", "phone"]:
            phrase_dir = os.path.join(dataset_path, phrase, "original")
            if os.path.exists(phrase_dir):
                videos = [f for f in os.listdir(phrase_dir) if f.endswith('.webm')]
                if videos:
                    sample_video = os.path.join(phrase_dir, videos[0])
                    print(f"   Found sample video: {sample_video}")
                    break
    
    if not sample_video:
        print("   ⚠️  No sample video found. Skipping prediction test.")
        print("   You can test manually with: curl -X POST -F 'file=@video.mp4' http://localhost:8000/predict_v2")
        return True
    
    # Test prediction endpoint
    print("\n4. Testing prediction endpoint...")
    success = test_predict_endpoint(sample_video)
    
    if success:
        print("\n🎉 All tests passed!")
        print("✅ Backend integration is working correctly")
        print("\nNext steps:")
        print("1. Test with mobile app")
        print("2. Verify end-to-end pipeline")
        print("3. Check prediction accuracy")
    else:
        print("\n❌ Some tests failed")
        print("Check the backend logs for more details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
