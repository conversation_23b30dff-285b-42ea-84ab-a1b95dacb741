#!/usr/bin/env python3
"""
Create minimal CTC models for testing mobile app connectivity.

This script creates basic CTC models with random weights that can be loaded
by the inference system, allowing the mobile app to connect and test the
full pipeline while proper models are being trained.
"""

import os
import sys
import yaml
import torch
import logging
from pathlib import Path

# Add backend to path
sys.path.append('backend')

from ctc_vsr.model_phoneme_ctc import PhonemeCtcModel
from ctc_vsr.model_char_ctc import CharCtcModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config():
    """Load CTC configuration."""
    config_path = "backend/ctc_vsr/config.yaml"
    if not os.path.exists(config_path):
        logger.error(f"Config file not found: {config_path}")
        return None
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def create_minimal_phoneme_model(config, output_path):
    """Create minimal phoneme CTC model."""
    logger.info("Creating minimal phoneme CTC model...")
    
    # Create model
    model = PhonemeCtcModel(config)
    
    # Create minimal checkpoint
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'epoch': 0,
        'best_val_loss': float('inf'),
        'config': config,
        'mode': 'phoneme',
        'model_info': {
            'type': 'minimal_placeholder',
            'created_for': 'mobile_app_testing',
            'note': 'This is a placeholder model with random weights for testing connectivity'
        }
    }
    
    # Save model
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    torch.save(checkpoint, output_path)
    logger.info(f"Minimal phoneme model saved: {output_path}")
    
    return True

def create_minimal_char_model(config, output_path):
    """Create minimal character CTC model."""
    logger.info("Creating minimal character CTC model...")
    
    # Create model
    model = CharCtcModel(config)
    
    # Create minimal checkpoint
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'epoch': 0,
        'best_val_loss': float('inf'),
        'config': config,
        'mode': 'character',
        'model_info': {
            'type': 'minimal_placeholder',
            'created_for': 'mobile_app_testing',
            'note': 'This is a placeholder model with random weights for testing connectivity'
        }
    }
    
    # Save model
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    torch.save(checkpoint, output_path)
    logger.info(f"Minimal character model saved: {output_path}")
    
    return True

def main():
    """Create minimal CTC models for testing."""
    logger.info("Creating minimal CTC models for mobile app testing...")
    
    # Load configuration
    config = load_config()
    if config is None:
        logger.error("Failed to load configuration")
        return 1
    
    # Define output paths
    phoneme_path = "artifacts/models/phoneme_ctc_best.pt"
    char_path = "artifacts/models/char_ctc_best.pt"
    
    try:
        # Create phoneme model
        if not create_minimal_phoneme_model(config, phoneme_path):
            logger.error("Failed to create phoneme model")
            return 1
        
        # Create character model
        if not create_minimal_char_model(config, char_path):
            logger.error("Failed to create character model")
            return 1
        
        logger.info("✅ Minimal CTC models created successfully!")
        logger.info(f"   Phoneme model: {phoneme_path}")
        logger.info(f"   Character model: {char_path}")
        logger.info("")
        logger.info("📱 Your mobile app should now be able to connect and test the CTC pipeline.")
        logger.info("⚠️  Note: These are placeholder models with random weights.")
        logger.info("   For production use, train proper models using the training scripts.")
        
        return 0
        
    except Exception as e:
        logger.error(f"Failed to create models: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
