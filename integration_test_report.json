{"timestamp": "2025-08-26 21:15:42", "total_test_time": "0.01s", "model_directory": "artifacts/prototype_10p_v1", "backend_url": "http://localhost:8000", "expected_phrases": ["doctor", "glasses", "help", "i_m_hot", "i_need_to_move", "my_back_hurts", "my_chest_hurts", "my_mouth_is_dry", "phone", "pillow"], "results": {"model_artifacts": {"passed": false}, "backend_startup": {"passed": false}, "inference_performance": {"passed": false}, "mobile_app_compatibility": {"passed": false}}, "summary": {"tests_passed": 0, "total_tests": 4, "success_rate": 0.0}}