#!/usr/bin/env python3
"""
Final Integration Test for Lightweight VSR System
Tests the complete pipeline from training to inference
"""

import os
import sys
import json
import time
import subprocess
import requests
from pathlib import Path

def check_training_artifacts():
    """Check if training artifacts exist"""
    print("🔍 Checking training artifacts...")
    
    artifacts_dir = Path("artifacts/prototype_10p_v1")
    required_files = ["best.ckpt", "metrics.json"]
    
    missing_files = []
    for file in required_files:
        if not (artifacts_dir / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    # Check metrics
    try:
        with open(artifacts_dir / "metrics.json") as f:
            metrics = json.load(f)
        
        accuracy = metrics["overall"]["accuracy"]
        print(f"✅ Training artifacts found!")
        print(f"   Model accuracy: {accuracy:.1%}")
        print(f"   Metrics file: ✅")
        print(f"   Model checkpoint: ✅")
        return True
        
    except Exception as e:
        print(f"❌ Error reading metrics: {e}")
        return False

def test_backend_inference():
    """Test backend inference with a real video"""
    print("\n🔍 Testing backend inference...")
    
    # Set environment
    os.environ['VSR_IMPL'] = 'lightweight'
    
    # Start backend
    proc = subprocess.Popen([
        sys.executable, "-m", "uvicorn", 
        "backend.api.app:app", 
        "--host", "127.0.0.1", 
        "--port", "8002"
    ], 
    stdout=subprocess.PIPE, 
    stderr=subprocess.PIPE,
    env=os.environ.copy()
    )
    
    try:
        # Wait for startup
        time.sleep(8)
        
        # Test health
        response = requests.get("http://127.0.0.1:8002/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Health check failed: {response.status_code}")
            return False
        
        print("✅ Backend started successfully")
        
        # Find a test video
        test_videos = list(Path("test_videos").glob("*.webm"))[:1]
        if not test_videos:
            print("⚠️  No test videos found, skipping inference test")
            return True
        
        test_video = test_videos[0]
        print(f"📹 Testing with video: {test_video.name}")
        
        # Test inference
        with open(test_video, 'rb') as f:
            files = {'file': (test_video.name, f, 'video/webm')}
            response = requests.post(
                "http://127.0.0.1:8002/predict_v2", 
                files=files, 
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Inference successful!")
            print(f"   Prediction: {result.get('prediction', 'unknown')}")
            print(f"   Confidence: {result.get('confidence', 0):.2f}")
            return True
        else:
            print(f"❌ Inference failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Backend test error: {e}")
        return False
        
    finally:
        try:
            proc.terminate()
            proc.wait(timeout=5)
        except:
            proc.kill()

def check_mobile_compatibility():
    """Check mobile app compatibility"""
    print("\n🔍 Checking mobile app compatibility...")
    
    mobile_app = Path("mobile_app")
    if not mobile_app.exists():
        print("⚠️  Mobile app directory not found")
        return True
    
    # Check key files
    key_files = ["App.js", "package.json"]
    for file in key_files:
        if (mobile_app / file).exists():
            print(f"✅ {file} found")
        else:
            print(f"⚠️  {file} not found")
    
    return True

def main():
    print("🚀 Final Integration Test - Lightweight VSR System")
    print("=" * 60)
    
    results = []
    
    # Test 1: Training artifacts
    results.append(("Training Artifacts", check_training_artifacts()))
    
    # Test 2: Backend inference
    results.append(("Backend Inference", test_backend_inference()))
    
    # Test 3: Mobile compatibility
    results.append(("Mobile Compatibility", check_mobile_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Final Integration Test Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
        if success:
            passed += 1
    
    print(f"\nTests Passed: {passed}/{total} ({passed/total*100:.0f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! System is ready for deployment!")
        return True
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Review issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
