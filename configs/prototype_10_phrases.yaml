# 2-Hour Prototype - 10 Phrase Configuration
# Optimized for fast training and deployment

phrases:
  - "doctor"
  - "glasses"
  - "help"
  - "i_m_hot"
  - "i_need_to_move"
  - "my_back_hurts"
  - "my_chest_hurts"
  - "my_mouth_is_dry"
  - "phone"
  - "pillow"

# Video processing parameters
frames: 32
height: 96
width: 96
grayscale: true

# Model parameters
confidence_threshold: 0.55

# Training parameters (optimized for speed)
batch_size: 16
learning_rate: 0.002  # Slightly higher for faster convergence
weight_decay: 0.01
epochs: 8  # Reduced for 2-hour constraint

# Data splits (speaker-wise to prevent leakage)
val_split: 0.15  # Slightly larger validation set
test_split: 0.15

# Augmentation parameters (reduced for faster training)
brightness_contrast_range: 0.10  # ±10%
scale_range: 0.08               # ±8%
vertical_jitter: 6              # ±6 pixels
temporal_jitter: 3              # ±3 frames

# Model architecture
model:
  backbone: "mobile3d_tiny"
  hidden_dim: 256
  num_gru_layers: 2
  dropout: 0.2
  
# Loss function
loss:
  type: "weighted_cross_entropy"
  class_weights: "auto"
  
# Optimizer
optimizer:
  type: "adamw"
  lr: 0.002
  weight_decay: 0.01
  
# Scheduler
scheduler:
  type: "cosine"
  warmup_epochs: 1  # Reduced warmup
  
# Mixed precision training
amp: true

# Inference
inference:
  batch_size: 1
  device: "auto"
