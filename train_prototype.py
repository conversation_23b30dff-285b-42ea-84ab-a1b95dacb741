#!/usr/bin/env python3
"""
2-Hour Prototype Training Script
Optimized for fast training with 10 phrases
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import torch
        import torchvision
        import cv2
        import yaml
        print("✅ Core requirements available")
        return True
    except ImportError as e:
        print(f"❌ Missing requirement: {e}")
        return False

def install_requirements():
    """Install lightweight VSR requirements"""
    print("Installing requirements...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", 
            "backend/lightweight_vsr/requirements.txt"
        ], check=True)
        print("✅ Requirements installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def run_training():
    """Run the training with optimized parameters for prototype"""

    # Training parameters optimized for 2-hour constraint
    manifest_path = "manifest.csv"
    config_path = "configs/prototype_10_phrases.yaml"
    output_dir = "artifacts/prototype_10p_v1"

    # Verify files exist
    if not os.path.exists(manifest_path):
        print(f"❌ Manifest not found: {manifest_path}")
        return False

    if not os.path.exists(config_path):
        print(f"❌ Config not found: {config_path}")
        return False

    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # Set PYTHONPATH to include current directory
    env = os.environ.copy()
    env['PYTHONPATH'] = os.getcwd()

    # Training command
    cmd = [
        sys.executable, "backend/lightweight_vsr/train.py",
        "--manifest", manifest_path,
        "--config", config_path,
        "--out_dir", output_dir,
        "--epochs", "8",  # Reduced for speed
        "--batch_size", "16",
        "--val_split", "0.15",
        "--test_split", "0.15",
        "--num_workers", "4",
        "--amp", "1"  # Use mixed precision for speed
    ]
    
    print("🚀 Starting training...")
    print(f"Command: {' '.join(cmd)}")
    print(f"Output directory: {output_dir}")
    print(f"Expected duration: 45-60 minutes")
    
    start_time = time.time()
    
    try:
        # Run training with environment
        result = subprocess.run(cmd, check=True, capture_output=False, env=env)

        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ Training completed in {duration/60:.1f} minutes")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Training failed: {e}")
        return False
    except KeyboardInterrupt:
        print("❌ Training interrupted by user")
        return False

def main():
    print("=== 2-Hour Prototype Training ===")
    print("Training lightweight VSR model with 10 phrases")
    
    # Check if we're in the right directory
    if not os.path.exists("backend/lightweight_vsr/train.py"):
        print("❌ Please run from project root directory")
        return False
    
    # Check requirements
    if not check_requirements():
        print("Installing missing requirements...")
        if not install_requirements():
            return False
    
    # Run training
    success = run_training()
    
    if success:
        print("\n🎉 Phase 2 Complete!")
        print("✅ Model trained successfully")
        print("✅ Ready for Phase 3: Model Export")
        print("\nNext steps:")
        print("1. Export model to TorchScript")
        print("2. Integrate with backend")
        print("3. Test end-to-end pipeline")
    else:
        print("\n❌ Training failed")
        print("Check logs and try again")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
