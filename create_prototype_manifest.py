#!/usr/bin/env python3
"""
Create manifest.csv for the 2-hour prototype using YES top 10 dataset
Target: 100 video samples per phrase (1000 total samples)
"""

import os
import csv
import random
from pathlib import Path

# Target phrases from the YES top 10 dataset
TARGET_PHRASES = [
    "doctor", "glasses", "help", "i_m_hot", "i_need_to_move",
    "my_back_hurts", "my_chest_hurts", "my_mouth_is_dry", "phone", "pillow"
]

# Base path to the dataset
DATASET_BASE = os.path.expanduser("~/Desktop/entire dataset 23.8.25/YES top 10")

def extract_metadata_from_filename(filename):
    """Extract metadata from filename format: phrase__speaker__age__gender__ethnicity__timestamp.ext"""
    try:
        basename = os.path.basename(filename)
        name_without_ext = os.path.splitext(basename)[0]
        parts = name_without_ext.split('__')

        if len(parts) >= 5:
            return {
                'speaker_id': parts[1] if len(parts) > 1 else 'unknown',
                'age_group': parts[2] if len(parts) > 2 else 'unknown',
                'gender': parts[3] if len(parts) > 3 else 'unknown',
                'ethnicity': parts[4] if len(parts) > 4 else 'unknown',
                'lighting': 'indoor_bright'  # Default value
            }
    except:
        pass

    return {
        'speaker_id': 'unknown',
        'age_group': 'unknown',
        'gender': 'unknown',
        'ethnicity': 'unknown',
        'lighting': 'indoor_bright'
    }

def collect_videos_for_phrase(phrase, target_count=100):
    """Collect video files for a specific phrase"""
    phrase_dir = os.path.join(DATASET_BASE, phrase, "original")

    if not os.path.exists(phrase_dir):
        print(f"Warning: Directory not found: {phrase_dir}")
        return []

    # Get all video files with metadata
    video_entries = []
    for file in os.listdir(phrase_dir):
        if file.lower().endswith(('.mp4', '.avi', '.mov', '.webm')):
            full_path = os.path.join(phrase_dir, file)
            if os.path.exists(full_path):
                metadata = extract_metadata_from_filename(file)
                video_entries.append({
                    'video_path': full_path,
                    'phrase': phrase,
                    **metadata
                })

    print(f"Found {len(video_entries)} videos for '{phrase}'")

    # Randomly sample target_count videos (or all if less than target)
    if len(video_entries) > target_count:
        video_entries = random.sample(video_entries, target_count)

    print(f"Selected {len(video_entries)} videos for '{phrase}'")
    return video_entries

def create_manifest():
    """Create the manifest.csv file"""
    manifest_path = "manifest.csv"

    print("Creating prototype manifest for 2-hour training...")
    print(f"Target: 100 videos per phrase, 10 phrases = 1000 total videos")

    # Set random seed for reproducibility
    random.seed(42)

    all_entries = []

    for phrase in TARGET_PHRASES:
        print(f"\nProcessing phrase: {phrase}")
        video_entries = collect_videos_for_phrase(phrase, target_count=100)
        all_entries.extend(video_entries)

    # Shuffle all entries for better training distribution
    random.shuffle(all_entries)

    # Write to CSV
    with open(manifest_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['video_path', 'phrase', 'speaker_id', 'age_group', 'gender', 'ethnicity', 'lighting']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for entry in all_entries:
            writer.writerow(entry)

    print(f"\nManifest created: {manifest_path}")
    print(f"Total entries: {len(all_entries)}")

    # Print summary by phrase
    phrase_counts = {}
    for entry in all_entries:
        phrase = entry['phrase']
        phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1

    print("\nSummary by phrase:")
    for phrase, count in sorted(phrase_counts.items()):
        print(f"  {phrase}: {count} videos")

    return manifest_path

def validate_manifest(manifest_path):
    """Validate that all video files in manifest exist"""
    print(f"\nValidating manifest: {manifest_path}")
    
    missing_files = []
    total_files = 0
    
    with open(manifest_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            total_files += 1
            video_path = row['video_path']
            if not os.path.exists(video_path):
                missing_files.append(video_path)
    
    print(f"Total files in manifest: {total_files}")
    print(f"Missing files: {len(missing_files)}")
    
    if missing_files:
        print("Missing files:")
        for file in missing_files[:10]:  # Show first 10
            print(f"  {file}")
        if len(missing_files) > 10:
            print(f"  ... and {len(missing_files) - 10} more")
        return False
    else:
        print("All files exist and are accessible!")
        return True

if __name__ == "__main__":
    print("=== Prototype Manifest Creator ===")
    print("Creating manifest for 2-hour lipreading prototype")
    
    # Create manifest
    manifest_path = create_manifest()
    
    # Validate manifest
    is_valid = validate_manifest(manifest_path)
    
    if is_valid:
        print(f"\n✅ SUCCESS: Manifest created and validated!")
        print(f"Ready for training with {manifest_path}")
    else:
        print(f"\n❌ WARNING: Some files are missing. Check paths and try again.")
