#!/usr/bin/env python3
"""
Production Deployment Script for Lightweight VSR System
Prepares and deploys the trained model for production use
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional

class ProductionDeployer:
    def __init__(self, model_dir: str = "artifacts/prototype_10p_v1"):
        self.model_dir = Path(model_dir)
        self.production_dir = Path("production")
        self.backup_dir = Path("backups")
        
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def validate_model(self) -> bool:
        """Validate that the trained model is ready for production"""
        self.log("🔍 Validating trained model...")
        
        required_files = [
            "best_model.pth",
            "config.yaml",
            "vocab.json",
            "training_log.json"
        ]
        
        if not self.model_dir.exists():
            self.log(f"❌ Model directory not found: {self.model_dir}", "ERROR")
            return False
            
        missing_files = []
        for file in required_files:
            if not (self.model_dir / file).exists():
                missing_files.append(file)
                
        if missing_files:
            self.log(f"❌ Missing required files: {missing_files}", "ERROR")
            return False
            
        # Check training log for completion
        try:
            with open(self.model_dir / "training_log.json", 'r') as f:
                log = json.load(f)
                
            if not log.get('training_completed', False):
                self.log("❌ Training not completed", "ERROR")
                return False
                
            epochs = log.get('epochs', [])
            if len(epochs) < 8:
                self.log(f"❌ Insufficient training epochs: {len(epochs)}/8", "ERROR")
                return False
                
            self.log("✅ Model validation passed")
            self.log(f"   Training epochs: {len(epochs)}")
            self.log(f"   Best validation F1: {log.get('best_val_f1', 'unknown')}")
            return True
            
        except Exception as e:
            self.log(f"❌ Error reading training log: {e}", "ERROR")
            return False
    
    def create_production_structure(self) -> bool:
        """Create production directory structure"""
        self.log("📁 Creating production directory structure...")
        
        try:
            # Create main production directory
            self.production_dir.mkdir(exist_ok=True)
            
            # Create subdirectories
            subdirs = [
                "models",
                "configs", 
                "logs",
                "mobile_app",
                "backend"
            ]
            
            for subdir in subdirs:
                (self.production_dir / subdir).mkdir(exist_ok=True)
                
            self.log("✅ Production directory structure created")
            return True
            
        except Exception as e:
            self.log(f"❌ Error creating production structure: {e}", "ERROR")
            return False
    
    def copy_model_artifacts(self) -> bool:
        """Copy model artifacts to production directory"""
        self.log("📦 Copying model artifacts...")
        
        try:
            model_prod_dir = self.production_dir / "models" / "lightweight_vsr"
            model_prod_dir.mkdir(exist_ok=True)
            
            # Copy model files
            files_to_copy = [
                "best_model.pth",
                "config.yaml", 
                "vocab.json",
                "training_log.json"
            ]
            
            for file in files_to_copy:
                src = self.model_dir / file
                dst = model_prod_dir / file
                shutil.copy2(src, dst)
                self.log(f"   Copied: {file}")
                
            # Create model metadata
            metadata = {
                "model_name": "lightweight_vsr_prototype",
                "version": "1.0.0",
                "training_date": self.get_training_date(),
                "phrases": self.get_supported_phrases(),
                "model_size_mb": self.get_model_size_mb(),
                "deployment_date": self.get_current_timestamp()
            }
            
            with open(model_prod_dir / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
                
            self.log("✅ Model artifacts copied successfully")
            return True
            
        except Exception as e:
            self.log(f"❌ Error copying model artifacts: {e}", "ERROR")
            return False
    
    def prepare_backend_config(self) -> bool:
        """Prepare backend configuration for production"""
        self.log("⚙️  Preparing backend configuration...")
        
        try:
            backend_config = {
                "vsr_implementation": "lightweight",
                "model_path": "production/models/lightweight_vsr/best_model.pth",
                "config_path": "production/models/lightweight_vsr/config.yaml",
                "vocab_path": "production/models/lightweight_vsr/vocab.json",
                "confidence_threshold": 0.55,
                "max_video_size_mb": 50,
                "allowed_formats": [".mp4", ".webm", ".avi", ".mov"],
                "cors_origins": ["*"],
                "log_level": "INFO"
            }
            
            config_path = self.production_dir / "configs" / "backend_config.json"
            with open(config_path, 'w') as f:
                json.dump(backend_config, f, indent=2)
                
            # Create environment file
            env_content = """# Production Environment Configuration
VSR_IMPL=lightweight
MODEL_PATH=production/models/lightweight_vsr/best_model.pth
CONFIG_PATH=production/models/lightweight_vsr/config.yaml
VOCAB_PATH=production/models/lightweight_vsr/vocab.json
CONFIDENCE_THRESHOLD=0.55
LOG_LEVEL=INFO
"""
            
            with open(self.production_dir / ".env", 'w') as f:
                f.write(env_content)
                
            self.log("✅ Backend configuration prepared")
            return True
            
        except Exception as e:
            self.log(f"❌ Error preparing backend config: {e}", "ERROR")
            return False
    
    def create_mobile_app_package(self) -> bool:
        """Create mobile app package with production configuration"""
        self.log("📱 Creating mobile app package...")
        
        try:
            mobile_dir = self.production_dir / "mobile_app"
            
            # Copy mobile app files if they exist
            if Path("LipreadingApp").exists():
                shutil.copytree("LipreadingApp", mobile_dir / "LipreadingApp", dirs_exist_ok=True)
                
                # Update app configuration for production
                app_config = {
                    "backend_url": "http://YOUR_SERVER_IP:8000",
                    "model_version": "1.0.0",
                    "supported_phrases": self.get_supported_phrases(),
                    "confidence_threshold": 0.55,
                    "max_recording_duration": 8,
                    "video_quality": "medium"
                }
                
                config_path = mobile_dir / "LipreadingApp" / "production_config.json"
                with open(config_path, 'w') as f:
                    json.dump(app_config, f, indent=2)
                    
                self.log("✅ Mobile app package created")
            else:
                self.log("⚠️  Mobile app directory not found, skipping", "WARNING")
                
            return True
            
        except Exception as e:
            self.log(f"❌ Error creating mobile app package: {e}", "ERROR")
            return False
    
    def create_deployment_scripts(self) -> bool:
        """Create deployment and startup scripts"""
        self.log("📜 Creating deployment scripts...")
        
        try:
            # Backend startup script
            backend_script = """#!/bin/bash
# Backend Startup Script for Production

echo "🚀 Starting Lightweight VSR Backend..."

# Set environment variables
export VSR_IMPL=lightweight
export MODEL_PATH=production/models/lightweight_vsr/best_model.pth
export CONFIG_PATH=production/models/lightweight_vsr/config.yaml
export VOCAB_PATH=production/models/lightweight_vsr/vocab.json

# Activate virtual environment
source .venv/bin/activate

# Start backend server
echo "Starting backend on port 8000..."
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload

echo "Backend started successfully!"
"""
            
            with open(self.production_dir / "start_backend.sh", 'w') as f:
                f.write(backend_script)
            os.chmod(self.production_dir / "start_backend.sh", 0o755)
            
            # Docker deployment script
            docker_script = """#!/bin/bash
# Docker Deployment Script

echo "🐳 Building Docker image for Lightweight VSR..."

# Build Docker image
docker build -t lightweight-vsr:latest .

# Run container
docker run -d \\
  --name lightweight-vsr \\
  -p 8000:8000 \\
  -v $(pwd)/production:/app/production \\
  -e VSR_IMPL=lightweight \\
  lightweight-vsr:latest

echo "Docker container started successfully!"
echo "Backend available at: http://localhost:8000"
"""
            
            with open(self.production_dir / "deploy_docker.sh", 'w') as f:
                f.write(docker_script)
            os.chmod(self.production_dir / "deploy_docker.sh", 0o755)
            
            self.log("✅ Deployment scripts created")
            return True
            
        except Exception as e:
            self.log(f"❌ Error creating deployment scripts: {e}", "ERROR")
            return False
    
    def create_backup(self) -> bool:
        """Create backup of current production if it exists"""
        if self.production_dir.exists():
            self.log("💾 Creating backup of existing production...")
            
            try:
                self.backup_dir.mkdir(exist_ok=True)
                timestamp = self.get_current_timestamp().replace(":", "-")
                backup_path = self.backup_dir / f"production_backup_{timestamp}"
                
                shutil.copytree(self.production_dir, backup_path)
                self.log(f"✅ Backup created: {backup_path}")
                return True
                
            except Exception as e:
                self.log(f"❌ Error creating backup: {e}", "ERROR")
                return False
        return True
    
    def get_training_date(self) -> str:
        """Get training date from model directory"""
        try:
            stat = os.stat(self.model_dir / "best_model.pth")
            import time
            return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(stat.st_mtime))
        except:
            return "unknown"
    
    def get_supported_phrases(self) -> list:
        """Get supported phrases from vocab"""
        try:
            with open(self.model_dir / "vocab.json", 'r') as f:
                vocab = json.load(f)
            return list(vocab.keys())
        except:
            return []
    
    def get_model_size_mb(self) -> float:
        """Get model size in MB"""
        try:
            size_bytes = os.path.getsize(self.model_dir / "best_model.pth")
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0.0
    
    def get_current_timestamp(self) -> str:
        """Get current timestamp"""
        import time
        return time.strftime("%Y-%m-%d %H:%M:%S")
    
    def deploy(self) -> bool:
        """Run complete deployment process"""
        self.log("🚀 Starting Production Deployment")
        self.log("=" * 60)
        
        steps = [
            ("Validate Model", self.validate_model),
            ("Create Backup", self.create_backup),
            ("Create Production Structure", self.create_production_structure),
            ("Copy Model Artifacts", self.copy_model_artifacts),
            ("Prepare Backend Config", self.prepare_backend_config),
            ("Create Mobile App Package", self.create_mobile_app_package),
            ("Create Deployment Scripts", self.create_deployment_scripts)
        ]
        
        for step_name, step_func in steps:
            self.log(f"\n--- {step_name} ---")
            if not step_func():
                self.log(f"❌ Deployment failed at: {step_name}", "ERROR")
                return False
            
        self.log("\n" + "=" * 60)
        self.log("🎉 Production Deployment Complete!")
        self.log("=" * 60)
        self.log(f"Production directory: {self.production_dir.absolute()}")
        self.log("Next steps:")
        self.log("1. Review production/configs/backend_config.json")
        self.log("2. Update mobile app backend URL in production_config.json")
        self.log("3. Run: ./production/start_backend.sh")
        self.log("4. Test with: python test_complete_integration.py")
        
        return True

if __name__ == "__main__":
    model_dir = sys.argv[1] if len(sys.argv) > 1 else "artifacts/prototype_10p_v1"
    
    deployer = ProductionDeployer(model_dir)
    success = deployer.deploy()
    
    sys.exit(0 if success else 1)
