{"name": "expo-av", "version": "15.1.7", "description": "Expo universal module for Audio and Video playback", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["expo", "react-native", "audio", "video"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-av"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/av/", "devDependencies": {"expo-module-scripts": "^4.1.8"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}