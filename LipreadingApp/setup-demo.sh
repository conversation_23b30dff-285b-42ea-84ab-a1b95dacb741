#!/bin/bash

# ICU Lipreading App - Demo Setup Script
# This script sets up the complete demo environment

echo "🚀 ICU Lipreading App - Demo Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the LipreadingApp directory"
    exit 1
fi

print_info "Setting up ICU Lipreading Demo Environment..."

# 1. Check Node.js installation
echo ""
echo "1. Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_status "Node.js found: $NODE_VERSION"
else
    print_error "Node.js not found. Please install Node.js first."
    exit 1
fi

# 2. Check npm installation
echo ""
echo "2. Checking npm installation..."
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_status "npm found: $NPM_VERSION"
else
    print_error "npm not found. Please install npm first."
    exit 1
fi

# 3. Install dependencies
echo ""
echo "3. Installing dependencies..."
if npm install; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# 4. Check Expo CLI
echo ""
echo "4. Checking Expo CLI..."
if command -v npx expo &> /dev/null; then
    print_status "Expo CLI available"
else
    print_warning "Installing Expo CLI..."
    npm install -g @expo/cli
fi

# 5. Get network IP
echo ""
echo "5. Detecting network configuration..."
if command -v ifconfig &> /dev/null; then
    LAPTOP_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | head -1 | awk '{print $2}')
    if [ ! -z "$LAPTOP_IP" ]; then
        print_status "Laptop IP detected: $LAPTOP_IP"
        
        # Update config.ts with detected IP
        if [ -f "config.ts" ]; then
            sed -i.bak "s/API_BASE_URL: 'http:\/\/[0-9.]*:8000'/API_BASE_URL: 'http:\/\/$LAPTOP_IP:8000'/" config.ts
            print_status "Updated config.ts with IP: $LAPTOP_IP"
        fi
    else
        print_warning "Could not detect IP automatically"
        print_info "Please update config.ts manually with your laptop's IP"
    fi
else
    print_warning "ifconfig not available. Please check network configuration manually."
fi

# 6. Check backend server
echo ""
echo "6. Checking backend server..."
BACKEND_URL="http://${LAPTOP_IP:-***********}:8000"

if curl -s "$BACKEND_URL/health" > /dev/null 2>&1; then
    print_status "Backend server is running at $BACKEND_URL"
    
    # Check CTC health
    if curl -s "$BACKEND_URL/ctc/health" > /dev/null 2>&1; then
        print_status "CTC endpoints are available"
    else
        print_warning "CTC endpoints not responding (this is expected if models aren't trained)"
    fi
else
    print_warning "Backend server not running at $BACKEND_URL"
    print_info "Start the backend server with:"
    print_info "  cd '../' && export VSR_IMPL=ctc && python -m backend.api.app"
fi

# 7. Run tests
echo ""
echo "7. Running connectivity tests..."
if [ -f "test-connection.js" ]; then
    if node test-connection.js; then
        print_status "Connectivity tests passed"
    else
        print_warning "Some connectivity tests failed (check backend server)"
    fi
else
    print_warning "Test files not found"
fi

# 8. Final setup summary
echo ""
echo "🎯 Demo Setup Summary"
echo "===================="

if [ ! -z "$LAPTOP_IP" ]; then
    echo "📱 Mobile App:"
    echo "   - Dependencies: ✅ Installed"
    echo "   - Configuration: ✅ Updated with IP $LAPTOP_IP"
    echo "   - Ready to start: ✅ Yes"
    echo ""
    echo "🖥️  Backend Server:"
    echo "   - URL: $BACKEND_URL"
    if curl -s "$BACKEND_URL/health" > /dev/null 2>&1; then
        echo "   - Status: ✅ Running"
    else
        echo "   - Status: ❌ Not running"
    fi
    echo ""
fi

echo "🚀 Next Steps:"
echo "1. Start backend server (if not running):"
echo "   cd '../' && export VSR_IMPL=ctc && python -m backend.api.app"
echo ""
echo "2. Start Expo development server:"
echo "   npx expo start"
echo ""
echo "3. Install Expo Go on your phone and scan the QR code"
echo ""
echo "4. Follow the demo script in DEMO_SCRIPT.md"

# 9. Optional: Start Expo server
echo ""
read -p "🤔 Start Expo development server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Starting Expo development server..."
    print_info "Scan the QR code with Expo Go app on your phone"
    npx expo start
else
    print_info "Run 'npx expo start' when ready to begin demo"
fi

print_status "Demo setup complete! 🎉"
