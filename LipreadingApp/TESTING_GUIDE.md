# ICU Lipreading Mobile App - Testing Guide

## 🎯 Overview
This guide provides comprehensive testing instructions for the ICU Lipreading mobile app with the new SMHS UI design and improved server connectivity.

## 🔧 Recent Fixes Applied

### 1. Server Connection Issues ✅
- **Fixed network timeout errors** - Increased timeout from 10s to 15s for health checks, 30s for predictions
- **Enhanced error handling** - Better error messages for network failures and timeouts
- **Added network security configuration** - Configured app.json to allow HTTP connections to local server
- **Improved logging** - Added detailed console logging for debugging connection issues

### 2. UI Design Implementation ✅
- **SMHS Color Scheme** - Purple (#6B46C1) and beige (#F5F1E8) color palette
- **Enhanced Oval Guide** - Larger, more prominent oval frame with better visibility
- **Improved Typography** - Better contrast and readability with SMHS styling
- **Updated Branding** - Changed title from "ICU Lipreading" to "Read My Lips"
- **Enhanced Visual Feedback** - Better recording indicators and progress displays

## 🧪 Testing Checklist

### Pre-Testing Setup
1. **Ensure server is running**:
   ```bash
   curl http://***********:8000/health
   ```
   Should return: `{"status":"healthy","vsr_impl":"ctc",...}`

2. **Run connection test**:
   ```bash
   cd LipreadingApp
   node test-server-connection.js
   ```

### Mobile App Testing

#### 1. App Launch & UI Verification
- [ ] App launches without crashes
- [ ] SMHS purple header with "Read My Lips" title
- [ ] Beige background color throughout app
- [ ] Settings button (gear icon) in top-left with purple background
- [ ] Mode button (AUTO/ICU/OPEN) in top-right with red accent color

#### 2. Camera Functionality
- [ ] Camera permission request appears
- [ ] Front camera activates by default
- [ ] Camera feed displays in oval frame
- [ ] Oval guide is beige/white color and clearly visible
- [ ] "Position your face in the oval" text appears below oval
- [ ] Flip camera button works (switches between front/back)

#### 3. Recording Functionality
- [ ] Record button is beige with purple border
- [ ] Tap record button starts countdown (3-2-1)
- [ ] Recording indicator appears (red background with beige text)
- [ ] Progress bar shows recording progress (purple fill)
- [ ] Recording stops automatically after configured duration
- [ ] Record button changes to red during recording

#### 4. Server Communication
- [ ] Connection status shows "Ready - CTC models loaded" or similar
- [ ] Video upload begins immediately after recording
- [ ] Processing indicator appears (purple background with beige text)
- [ ] No "Network request failed" errors
- [ ] No timeout errors during upload

#### 5. Recognition Modes Testing

**AUTO Mode:**
- [ ] Default mode on app launch
- [ ] Uses ICU confidence threshold (0.65)
- [ ] Displays predictions with confidence scores

**ICU Mode:**
- [ ] Switch to ICU mode via mode button
- [ ] Uses phoneme-based recognition
- [ ] Higher confidence threshold (0.65)
- [ ] Optimized for medical phrases

**OPEN Mode:**
- [ ] Switch to OPEN mode via mode button
- [ ] Uses character-based recognition
- [ ] Lower confidence threshold (0.55)
- [ ] General vocabulary recognition

#### 6. Results Display
- [ ] Prediction text appears in beige color
- [ ] Confidence percentage displayed
- [ ] Results container has purple background
- [ ] Text is clearly readable with good contrast

#### 7. Audio Feedback
- [ ] Recording start/stop sounds work
- [ ] Prediction results are spoken aloud
- [ ] Audio feedback respects device volume settings

## 🐛 Common Issues & Solutions

### Network Connection Errors
**Symptoms:** "Network request failed", "Connection timeout"
**Solutions:**
1. Verify server is running: `curl http://***********:8000/health`
2. Check WiFi connection on mobile device
3. Ensure mobile device and laptop are on same network
4. Restart Expo Go app

### Camera Issues
**Symptoms:** Black screen, no camera feed
**Solutions:**
1. Grant camera permissions in device settings
2. Close other apps using camera
3. Restart Expo Go app
4. Try switching camera (front/back)

### Recording Issues
**Symptoms:** Recording doesn't start, no video upload
**Solutions:**
1. Check camera and microphone permissions
2. Ensure sufficient storage space
3. Try shorter recording duration
4. Check server logs for upload errors

## 📱 Testing Scenarios

### Scenario 1: Basic ICU Phrase Recognition
1. Set mode to ICU
2. Record saying "I need help"
3. Verify prediction accuracy
4. Check confidence score > 65%

### Scenario 2: Open Vocabulary Testing
1. Set mode to OPEN
2. Record saying common words
3. Verify general recognition works
4. Check confidence score > 55%

### Scenario 3: Network Resilience
1. Start recording
2. Temporarily disconnect WiFi
3. Reconnect before upload
4. Verify graceful error handling

### Scenario 4: Multiple Recognition Modes
1. Test same phrase in all three modes
2. Compare prediction results
3. Verify confidence thresholds work correctly

## 🎉 Success Criteria

The app is considered fully functional when:
- ✅ All UI elements display with SMHS design
- ✅ Camera and recording work smoothly
- ✅ Server connection is stable (no timeouts)
- ✅ All three recognition modes function
- ✅ Predictions are accurate for test phrases
- ✅ Audio feedback works properly
- ✅ No crashes or major errors occur

## 📞 Support

If issues persist:
1. Check server logs for detailed error messages
2. Review mobile app console logs in Expo Go
3. Verify network configuration and IP addresses
4. Test with different phrases and recording durations

---

**Last Updated:** August 25, 2025
**Version:** 2.0 (SMHS Design + Enhanced Connectivity)
