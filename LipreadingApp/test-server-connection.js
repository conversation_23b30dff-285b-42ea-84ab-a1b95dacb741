#!/usr/bin/env node

/**
 * Test script to verify server connection and API endpoints
 * This simulates what the mobile app does when connecting to the server
 */

const CONFIG = {
  API_BASE_URL: 'http://***********:8000',
  ENDPOINTS: {
    HEALTH: '/health',
    CTC_PREDICT: '/ctc/predict',
    CTC_PREDICT_ICU: '/ctc/predict_icu',
    CTC_PREDICT_OPEN: '/ctc/predict_open',
  }
};

async function testHealthCheck() {
  console.log('🔍 Testing health check endpoint...');
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.HEALTH}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Health check successful:', result);
    return result;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return null;
  }
}

async function testCTCHealth() {
  console.log('🔍 Testing CTC health endpoint...');
  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/ctc/health`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`CTC health check failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ CTC health check successful:', result);
    return result;
  } catch (error) {
    console.error('❌ CTC health check failed:', error.message);
    return null;
  }
}

async function testNetworkConnectivity() {
  console.log('🌐 Testing basic network connectivity...');
  try {
    const response = await fetch(CONFIG.API_BASE_URL, {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/json',
      },
    });

    console.log(`✅ Server responded with status: ${response.status}`);
    return true;
  } catch (error) {
    console.error('❌ Network connectivity failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting server connection tests...');
  console.log(`📡 Testing connection to: ${CONFIG.API_BASE_URL}`);
  console.log('');

  // Test basic connectivity
  const networkOk = await testNetworkConnectivity();
  if (!networkOk) {
    console.log('❌ Basic network connectivity failed. Check server is running and IP is correct.');
    process.exit(1);
  }

  console.log('');

  // Test health endpoint
  const healthResult = await testHealthCheck();
  if (!healthResult) {
    console.log('❌ Health check failed. Server may not be responding correctly.');
    process.exit(1);
  }

  console.log('');

  // Test CTC health endpoint
  const ctcResult = await testCTCHealth();
  if (!ctcResult) {
    console.log('⚠️  CTC health check failed. CTC models may not be available.');
  }

  console.log('');
  console.log('📊 Test Summary:');
  console.log(`   Basic connectivity: ${networkOk ? '✅' : '❌'}`);
  console.log(`   Health endpoint: ${healthResult ? '✅' : '❌'}`);
  console.log(`   CTC endpoint: ${ctcResult ? '✅' : '❌'}`);
  
  if (healthResult) {
    console.log(`   VSR Implementation: ${healthResult.vsr_impl || 'unknown'}`);
    console.log(`   CTC Available: ${healthResult.ctc_available ? '✅' : '❌'}`);
  }

  if (ctcResult && ctcResult.models_loaded) {
    console.log(`   Phoneme model loaded: ${ctcResult.models_loaded.phoneme ? '✅' : '❌'}`);
    console.log(`   Character model loaded: ${ctcResult.models_loaded.character ? '✅' : '❌'}`);
  }

  console.log('');
  if (networkOk && healthResult) {
    console.log('🎉 Server connection tests completed successfully!');
    console.log('📱 The mobile app should be able to connect to the server.');
  } else {
    console.log('⚠️  Some tests failed. Check the server configuration.');
  }
}

// Run the tests
main().catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
