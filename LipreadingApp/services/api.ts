import CONFIG from '../config';

export interface PredictionResult {
  mode: string;
  prediction: string;
  confidence: number;
  topk?: Array<{ text: string; confidence: number }>;
  filename?: string;
  file_size?: number;
  endpoint?: string;
}

export interface HealthCheckResult {
  status: string;
  vsr_impl?: string;
  legacy_available?: boolean;
  lightweight_available?: boolean;
  ctc_available?: boolean;
  models_loaded?: {
    phoneme: boolean;
    character: boolean;
  };
  device?: string;
}

export type RecognitionMode = 'AUTO' | 'ICU' | 'OPEN';

class LipreadingAPI {
  private baseUrl: string;

  constructor(baseUrl: string = CONFIG.API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Check if the API server is healthy and available
   */
  async healthCheck(): Promise<HealthCheckResult> {
    try {
      console.log(`Attempting health check to: ${this.baseUrl}${CONFIG.ENDPOINTS.HEALTH}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      const response = await fetch(`${this.baseUrl}${CONFIG.ENDPOINTS.HEALTH}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log(`Health check response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Health check successful:', result);
      return result;
    } catch (error) {
      console.error('Health check error:', error);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Connection timeout - server not responding');
        }
        if (error.message.includes('Network request failed')) {
          throw new Error('Network error - check server is running and IP address is correct');
        }
        if (error.message.includes('TypeError: Network request failed')) {
          throw new Error('Network connection failed - check WiFi and server IP address');
        }
      }
      throw new Error(`Failed to connect to server: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if CTC models are available
   */
  async checkCTCHealth(): Promise<HealthCheckResult> {
    try {
      const response = await fetch(`${this.baseUrl}/ctc/health`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`CTC health check failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('CTC health check error:', error);
      throw new Error(`CTC models not available: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process video for lipreading prediction
   */
  async predict(videoUri: string, mode: RecognitionMode = 'AUTO'): Promise<PredictionResult> {
    try {
      console.log(`Starting prediction for video: ${videoUri}, mode: ${mode}`);

      // Determine endpoint based on mode
      let endpoint = CONFIG.ENDPOINTS.CTC_PREDICT;
      if (mode === 'ICU') {
        endpoint = CONFIG.ENDPOINTS.CTC_PREDICT_ICU;
      } else if (mode === 'OPEN') {
        endpoint = CONFIG.ENDPOINTS.CTC_PREDICT_OPEN;
      }

      console.log(`Using endpoint: ${this.baseUrl}${endpoint}`);

      // Create form data
      const formData = new FormData();
      formData.append('file', {
        uri: videoUri,
        type: 'video/mp4',
        name: 'lipreading_video.mp4',
      } as any);

      console.log('FormData created, making API request...');

      // Make API request with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type for FormData - let the browser set it with boundary
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log(`Prediction response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Prediction failed with error:', errorText);
        throw new Error(`Prediction failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result: PredictionResult = await response.json();
      console.log('Prediction successful:', result);

      // Validate result
      if (!result.prediction) {
        throw new Error('No prediction returned from server');
      }

      return result;
    } catch (error) {
      console.error('Prediction error:', error);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Prediction timeout - server took too long to respond');
        }
        if (error.message.includes('Network request failed')) {
          throw new Error('Network error during prediction - check connection');
        }
      }
      throw new Error(`Failed to process video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Test connection to the server
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log(`Testing connection to: ${this.baseUrl}${CONFIG.ENDPOINTS.HEALTH}`);
      await this.healthCheck();
      console.log('Connection test successful');
      return true;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * Get confidence threshold for a given mode
   */
  getConfidenceThreshold(mode: RecognitionMode): number {
    switch (mode) {
      case 'ICU':
        return CONFIG.CONFIDENCE_THRESHOLDS.ICU;
      case 'OPEN':
        return CONFIG.CONFIDENCE_THRESHOLDS.OPEN;
      case 'AUTO':
        // For AUTO mode, use ICU threshold as it's more conservative
        return CONFIG.CONFIDENCE_THRESHOLDS.ICU;
      default:
        return CONFIG.CONFIDENCE_THRESHOLDS.OPEN;
    }
  }

  /**
   * Check if prediction confidence meets threshold
   */
  isConfidenceAcceptable(confidence: number, mode: RecognitionMode): boolean {
    const threshold = this.getConfidenceThreshold(mode);
    return confidence >= threshold;
  }

  /**
   * Format prediction result for display
   */
  formatPrediction(result: PredictionResult): string {
    if (!result.prediction) {
      return 'No prediction';
    }

    // Capitalize first letter and clean up the text
    return result.prediction
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Get status message based on prediction result
   */
  getStatusMessage(result: PredictionResult, mode: RecognitionMode): string {
    if (!result.prediction) {
      return 'No prediction available';
    }

    const isAcceptable = this.isConfidenceAcceptable(result.confidence, mode);
    const threshold = this.getConfidenceThreshold(mode);
    
    if (isAcceptable) {
      return `Recognized: ${this.formatPrediction(result)} (${(result.confidence * 100).toFixed(1)}%)`;
    } else {
      return `Low confidence: ${(result.confidence * 100).toFixed(1)}% (need ${(threshold * 100).toFixed(0)}%)`;
    }
  }
}

// Export singleton instance
export const lipreadingAPI = new LipreadingAPI();
export default lipreadingAPI;
