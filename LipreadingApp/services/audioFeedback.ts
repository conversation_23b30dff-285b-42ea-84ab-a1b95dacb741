import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import CONFIG from '../config';
import { PredictionResult, RecognitionMode } from './api';

export interface AudioFeedbackOptions {
  enableTTS: boolean;
  enableSounds: boolean;
  volume: number;
  rate: number;
  pitch: number;
}

class AudioFeedbackService {
  private options: AudioFeedbackOptions = {
    enableTTS: true,
    enableSounds: true,
    volume: 1.0,
    rate: CONFIG.SPEECH.RATE,
    pitch: CONFIG.SPEECH.PITCH,
  };

  private sounds: { [key: string]: Audio.Sound } = {};

  constructor() {
    this.initializeAudio();
  }

  private async initializeAudio() {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
    } catch (error) {
      console.error('Failed to initialize audio:', error);
    }
  }

  /**
   * Update audio feedback options
   */
  updateOptions(newOptions: Partial<AudioFeedbackOptions>) {
    this.options = { ...this.options, ...newOptions };
  }

  /**
   * Speak text using text-to-speech
   */
  async speak(text: string, options?: Partial<Speech.SpeechOptions>): Promise<void> {
    if (!this.options.enableTTS || !text) {
      return;
    }

    try {
      // Stop any current speech
      await Speech.stop();

      const speechOptions: Speech.SpeechOptions = {
        language: CONFIG.SPEECH.LANGUAGE,
        pitch: this.options.pitch,
        rate: this.options.rate,
        volume: this.options.volume,
        ...options,
      };

      await Speech.speak(text, speechOptions);
    } catch (error) {
      console.error('Speech error:', error);
    }
  }

  /**
   * Provide audio feedback for prediction result
   */
  async providePredictionFeedback(
    result: PredictionResult,
    mode: RecognitionMode,
    isAcceptable: boolean
  ): Promise<void> {
    try {
      if (isAcceptable && result.prediction) {
        // High confidence - speak the prediction
        const formattedText = this.formatTextForSpeech(result.prediction);
        await this.speak(formattedText);
        
        // Optional success sound
        if (this.options.enableSounds) {
          await this.playSuccessSound();
        }
      } else {
        // Low confidence - ask to repeat
        await this.speak('Please repeat');
        
        // Optional error sound
        if (this.options.enableSounds) {
          await this.playErrorSound();
        }
      }
    } catch (error) {
      console.error('Feedback error:', error);
    }
  }

  /**
   * Provide feedback for different app states
   */
  async provideStateFeedback(state: 'recording_start' | 'recording_stop' | 'processing' | 'error'): Promise<void> {
    switch (state) {
      case 'recording_start':
        if (this.options.enableSounds) {
          await this.playRecordingStartSound();
        }
        break;
      case 'recording_stop':
        if (this.options.enableSounds) {
          await this.playRecordingStopSound();
        }
        break;
      case 'processing':
        // Optional processing sound or silence
        break;
      case 'error':
        await this.speak('Error occurred');
        if (this.options.enableSounds) {
          await this.playErrorSound();
        }
        break;
    }
  }

  /**
   * Format text for better speech synthesis
   */
  private formatTextForSpeech(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove special characters
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Play success sound (placeholder - would use actual sound files in production)
   */
  private async playSuccessSound(): Promise<void> {
    // In a real implementation, you would load and play an actual sound file
    // For now, we'll use a short beep-like TTS
    await this.speak('✓', { rate: 2.0, pitch: 1.5 });
  }

  /**
   * Play error sound (placeholder)
   */
  private async playErrorSound(): Promise<void> {
    // In a real implementation, you would load and play an actual sound file
    // For now, we'll use a short error-like TTS
    await this.speak('✗', { rate: 1.5, pitch: 0.8 });
  }

  /**
   * Play recording start sound (placeholder)
   */
  private async playRecordingStartSound(): Promise<void> {
    // Subtle audio cue for recording start
    // In production, use a short audio file
  }

  /**
   * Play recording stop sound (placeholder)
   */
  private async playRecordingStopSound(): Promise<void> {
    // Subtle audio cue for recording stop
    // In production, use a short audio file
  }

  /**
   * Stop all audio playback
   */
  async stopAll(): Promise<void> {
    try {
      await Speech.stop();
      // Stop any playing sounds
      for (const sound of Object.values(this.sounds)) {
        await sound.stopAsync();
      }
    } catch (error) {
      console.error('Error stopping audio:', error);
    }
  }

  /**
   * Check if speech is currently playing
   */
  async isSpeaking(): Promise<boolean> {
    try {
      return await Speech.isSpeakingAsync();
    } catch (error) {
      console.error('Error checking speech status:', error);
      return false;
    }
  }

  /**
   * Get available voices (if supported)
   */
  async getAvailableVoices(): Promise<Speech.Voice[]> {
    try {
      return await Speech.getAvailableVoicesAsync();
    } catch (error) {
      console.error('Error getting voices:', error);
      return [];
    }
  }

  /**
   * Set voice for speech synthesis
   */
  setVoice(voice: Speech.Voice | null) {
    // This would be used in speech options
    // Implementation depends on specific voice selection UI
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.stopAll();
    
    // Unload sounds
    for (const sound of Object.values(this.sounds)) {
      try {
        await sound.unloadAsync();
      } catch (error) {
        console.error('Error unloading sound:', error);
      }
    }
    
    this.sounds = {};
  }
}

// Export singleton instance
export const audioFeedback = new AudioFeedbackService();
export default audioFeedback;
