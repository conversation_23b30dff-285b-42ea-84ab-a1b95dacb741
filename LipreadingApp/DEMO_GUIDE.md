# ICU Lipreading Demo Guide

## Overview
This is a functioning lipreading prototype with live camera UI that captures video, sends it to CTC endpoints, and provides audio feedback.

## Setup Complete ✅

### Backend Server
- ✅ CORS enabled for mobile app access
- ✅ Server running on `http://***********:8000`
- ✅ Legacy models loaded (LipNet + ICU classifier)
- ✅ CTC infrastructure ready (models not trained yet)
- ✅ All endpoints accessible and responding

### Mobile App
- ✅ Expo React Native app created
- ✅ Camera, audio, and speech dependencies installed
- ✅ LiveLipReader component implemented
- ✅ API integration with error handling
- ✅ Enhanced audio feedback service
- ✅ Configuration management

## Demo Instructions

### 1. Start the Mobile App

```bash
# In the LipreadingApp directory
npx expo start
```

### 2. Connect Your Phone
1. Install Expo Go app on your phone
2. Ensure phone and laptop are on the same Wi-Fi network
3. Scan the QR code displayed in terminal/browser
4. The app should load on your phone

### 3. Test the App Features

#### Camera Functionality
- ✅ Front-facing camera preview with oval guide
- ✅ Camera flip functionality
- ✅ Recording indicator and countdown

#### Recording Process
1. Position your face in the oval guide
2. Tap the red record button
3. 3-second countdown will start
4. Record for 1 second automatically
5. Processing indicator will show
6. Audio feedback will play the result

#### Mode Switching
- **AUTO**: Tries ICU first, then OPEN, then fallback
- **ICU**: Medical vocabulary only (higher confidence threshold: 65%)
- **OPEN**: General vocabulary (lower confidence threshold: 55%)

#### Audio Feedback
- ✅ Text-to-speech for recognized phrases
- ✅ "Please repeat" for low confidence
- ✅ Recording start/stop audio cues
- ✅ Error handling with audio feedback

### 4. Expected Behavior (Current State)

Since CTC models are not trained yet:
- All predictions will use fallback responses
- You'll hear "Please repeat" for most attempts
- The app demonstrates full functionality pipeline
- Connection status shows "Connected but CTC models not available"

### 5. Demo Scenarios to Test

#### Basic Functionality
1. **Camera Permission**: Grant camera and microphone access
2. **Recording**: Test the 1-second video recording
3. **Mode Switching**: Cycle through AUTO → ICU → OPEN modes
4. **Camera Flip**: Switch between front and back camera
5. **Audio Feedback**: Verify TTS is working

#### Network Connectivity
1. **Connection Status**: Check green status message at bottom
2. **Server Response**: Verify app receives responses from server
3. **Error Handling**: Test with server offline (should show error)

#### User Experience
1. **Visual Feedback**: Recording indicators, countdown, processing spinner
2. **Audio Feedback**: Clear speech synthesis
3. **Responsive UI**: Smooth interactions and transitions

## Technical Details

### API Endpoints Tested
- ✅ `/health` - Server health check
- ✅ `/ctc/health` - CTC models status
- ✅ `/ctc/predict` - Auto mode selection
- ✅ `/ctc/predict_icu` - ICU mode only
- ✅ `/ctc/predict_open` - Open vocabulary mode
- ✅ `/predict` - Legacy fallback mode

### Configuration
- **Server IP**: `***********:8000` (update in `config.ts` if needed)
- **Recording Duration**: 1 second
- **Confidence Thresholds**: ICU 65%, OPEN 55%
- **Speech Settings**: English, normal rate and pitch

### File Structure
```
LipreadingApp/
├── components/
│   └── LiveLipReader.tsx     # Main camera component
├── services/
│   ├── api.ts               # API client service
│   └── audioFeedback.ts     # Audio feedback service
├── config.ts                # App configuration
├── test-connection.js       # Network connectivity test
├── test-endpoints.js        # API endpoints test
└── DEMO_GUIDE.md           # This guide
```

## Next Steps for Full Functionality

### 1. Train CTC Models (Optional for Demo)
```bash
# Build lexicon (already done)
bash scripts/ctc_build_lexicon.sh

# Train phoneme model (2-4 epochs for demo)
bash scripts/ctc_train_phoneme.sh

# Train character model (2-4 epochs for demo)
bash scripts/ctc_train_char.sh
```

### 2. Production Enhancements
- Add real audio files for feedback sounds
- Implement user settings for audio preferences
- Add video quality selection
- Implement offline mode with cached responses
- Add analytics and usage tracking

## Troubleshooting

### Connection Issues
1. Check laptop IP: `ifconfig | grep "inet " | grep -v 127.0.0.1`
2. Update `config.ts` with correct IP address
3. Ensure both devices on same Wi-Fi network
4. Check firewall settings on laptop

### App Issues
1. Clear Expo cache: `npx expo start --clear`
2. Restart Expo Go app on phone
3. Check console logs in Expo developer tools

### Server Issues
1. Restart server: `export VSR_IMPL=ctc && python -m backend.api.app`
2. Check server logs for errors
3. Verify dependencies are installed

## Success Criteria ✅

The demo successfully demonstrates:
- ✅ Live camera interface with professional UI
- ✅ Video recording and processing pipeline
- ✅ API integration with backend server
- ✅ Audio feedback system
- ✅ Mode switching functionality
- ✅ Error handling and user feedback
- ✅ Network connectivity and health checks
- ✅ Cross-platform mobile app (iOS/Android via Expo)

This prototype provides a complete foundation for ICU lipreading functionality and can be enhanced with trained models for production use.
