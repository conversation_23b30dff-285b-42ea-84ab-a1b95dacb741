// Test script to validate all CTC endpoints with sample data
// Run with: node test-endpoints.js

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://***********:8000';

// Mock video data for testing (since we can't easily upload real files from Node.js)
const createMockFormData = (filename) => {
  const FormData = require('form-data');
  const form = new FormData();
  
  // Create a small mock video file
  const mockVideoContent = Buffer.from('mock video data');
  form.append('file', mockVideoContent, {
    filename: filename,
    contentType: 'video/mp4'
  });
  
  return form;
};

async function testEndpoint(endpoint, description, mockFilename = 'test_video.mp4') {
  console.log(`\n🧪 Testing ${description}...`);
  console.log(`   Endpoint: ${endpoint}`);
  
  try {
    const form = createMockFormData(mockFilename);
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
      console.log(`   📊 Response:`, JSON.stringify(result, null, 4));
      return { success: true, result };
    } else {
      console.log(`   ⚠️  Status: ${response.status} ${response.statusText}`);
      console.log(`   📊 Response:`, JSON.stringify(result, null, 4));
      return { success: false, result };
    }
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testAllEndpoints() {
  console.log('🚀 Testing all CTC endpoints...');
  console.log(`Server: ${API_BASE_URL}`);
  
  const tests = [
    {
      endpoint: '/ctc/predict',
      description: 'CTC Auto Mode (best selection)',
      filename: 'auto_test.mp4'
    },
    {
      endpoint: '/ctc/predict_icu',
      description: 'CTC ICU Mode (phoneme + lexicon)',
      filename: 'icu_test.mp4'
    },
    {
      endpoint: '/ctc/predict_open',
      description: 'CTC Open Mode (character + open vocab)',
      filename: 'open_test.mp4'
    },
    {
      endpoint: '/predict',
      description: 'Legacy/Fallback Mode',
      filename: 'legacy_test.mp4'
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.endpoint, test.description, test.filename);
    results.push({ ...test, ...result });
  }
  
  // Summary
  console.log('\n📋 Test Summary:');
  console.log('=' .repeat(60));
  
  let passCount = 0;
  let totalCount = results.length;
  
  results.forEach((test, index) => {
    const status = test.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${index + 1}. ${test.description}: ${status}`);
    
    if (test.result) {
      const mode = test.result.mode || 'unknown';
      const confidence = test.result.confidence || 0;
      const prediction = test.result.text || test.result.prediction || 'none';
      
      console.log(`   Mode: ${mode}, Confidence: ${(confidence * 100).toFixed(1)}%, Prediction: "${prediction}"`);
      
      if (test.result.error) {
        console.log(`   Error: ${test.result.error}`);
      }
    }
    
    if (test.success) passCount++;
  });
  
  console.log('=' .repeat(60));
  console.log(`Results: ${passCount}/${totalCount} endpoints working`);
  
  if (passCount === totalCount) {
    console.log('🎉 All endpoints are functional!');
  } else {
    console.log('⚠️  Some endpoints have issues (expected if CTC models not trained)');
  }
  
  // Recommendations
  console.log('\n💡 Recommendations for mobile app:');
  
  const hasWorkingCTC = results.some(r => 
    r.endpoint.includes('/ctc/') && 
    r.result && 
    !r.result.error && 
    r.result.confidence > 0
  );
  
  if (hasWorkingCTC) {
    console.log('✅ CTC models are working - use CTC endpoints');
  } else {
    console.log('⚠️  CTC models not loaded - app will use fallback responses');
    console.log('   - The app will still work but with mock predictions');
    console.log('   - Train CTC models for real lipreading functionality');
  }
  
  console.log('\n🔧 Next steps:');
  console.log('1. Start the Expo development server: npx expo start');
  console.log('2. Scan QR code with Expo Go app on your phone');
  console.log('3. Test the camera and recording functionality');
  console.log('4. Verify audio feedback is working');
  console.log('5. Test different recognition modes (AUTO/ICU/OPEN)');
  
  return results;
}

// Add FormData polyfill for Node.js
if (typeof FormData === 'undefined') {
  global.FormData = require('form-data');
}

// Run the tests
testAllEndpoints().then(results => {
  const allWorking = results.every(r => r.success);
  process.exit(allWorking ? 0 : 1);
}).catch(error => {
  console.error('Test suite failed:', error);
  process.exit(1);
});
