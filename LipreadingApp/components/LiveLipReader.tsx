import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  ActivityIndicator,
  Animated,
  StatusBar,
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Audio } from 'expo-av';
import * as Speech from 'expo-speech';
import CONFIG from '../config';
import lipreadingAPI, { PredictionResult, RecognitionMode } from '../services/api';
import audioFeedback from '../services/audioFeedback';
import SettingsModal from './SettingsModal';
import LoadingScreen from './LoadingScreen';

const { width, height } = Dimensions.get('window');

const LiveLipReader: React.FC = () => {
  const [facing, setFacing] = useState<CameraType>('front');
  const [permission, requestPermission] = useCameraPermissions();
  const [audioPermission, setAudioPermission] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [mode, setMode] = useState<RecognitionMode>('AUTO');
  const [lastPrediction, setLastPrediction] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('Checking connection...');
  const [showSettings, setShowSettings] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState<number>(CONFIG.RECORDING.DURATION);
  const [recordingProgress, setRecordingProgress] = useState<number>(0);

  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const cameraRef = useRef<CameraView>(null);
  const recordingRef = useRef<Audio.Recording | null>(null);

  useEffect(() => {
    requestAudioPermission();
    testServerConnection();
  }, []);

  // Animation effects
  useEffect(() => {
    if (isRecording) {
      // Start pulsing animation for recording
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Stop animation
      pulseAnim.setValue(1);
    }
  }, [isRecording]);

  useEffect(() => {
    // Fade in connection status
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, [connectionStatus]);

  const testServerConnection = async () => {
    try {
      const isConnected = await lipreadingAPI.testConnection();
      if (isConnected) {
        setConnectionStatus('Connected to server');
        // Also test CTC models
        try {
          await lipreadingAPI.checkCTCHealth();
          setConnectionStatus('Ready - CTC models loaded');
        } catch (error) {
          setConnectionStatus('Connected but CTC models not available');
        }
      } else {
        setConnectionStatus('Cannot connect to server');
      }
    } catch (error) {
      setConnectionStatus('Connection failed');
    }
  };

  const requestAudioPermission = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      setAudioPermission(status === 'granted');
    } catch (error) {
      console.error('Error requesting audio permission:', error);
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || !audioPermission) return;

    try {
      setIsRecording(true);
      setCountdown(CONFIG.RECORDING.COUNTDOWN);

      // Start countdown
      const countdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(countdownInterval);
            recordVideo();
            return null;
          }
          return prev ? prev - 1 : null;
        });
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      setIsRecording(false);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const recordVideo = async () => {
    if (!cameraRef.current) return;

    try {
      // Provide audio feedback for recording start
      await audioFeedback.provideStateFeedback('recording_start');

      // Start progress tracking
      setRecordingProgress(0);
      const progressInterval = setInterval(() => {
        setRecordingProgress(prev => {
          const newProgress = prev + (100 / (recordingDuration * 10)); // Update every 100ms
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 100);

      // Record for configured duration
      const video = await cameraRef.current.recordAsync({
        maxDuration: recordingDuration,
        quality: CONFIG.RECORDING.QUALITY,
      });

      // Clear progress tracking
      clearInterval(progressInterval);
      setRecordingProgress(0);
      setIsRecording(false);

      // Provide audio feedback for recording stop
      await audioFeedback.provideStateFeedback('recording_stop');

      if (video?.uri) {
        await processVideo(video.uri);
      }
    } catch (error) {
      console.error('Error recording video:', error);
      setIsRecording(false);
      setRecordingProgress(0);
      await audioFeedback.provideStateFeedback('error');
      Alert.alert('Error', 'Failed to record video');
    }
  };

  const processVideo = async (videoUri: string) => {
    setIsProcessing(true);

    try {
      // Use the API service to process the video
      const result = await lipreadingAPI.predict(videoUri, mode);

      setLastPrediction(lipreadingAPI.formatPrediction(result));
      setConfidence(result.confidence || 0);

      // Provide audio feedback using the enhanced service
      const isAcceptable = lipreadingAPI.isConfidenceAcceptable(result.confidence, mode);
      await audioFeedback.providePredictionFeedback(result, mode, isAcceptable);

    } catch (error) {
      console.error('Error processing video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setLastPrediction(`Error: ${errorMessage}`);
      setConfidence(0);
      await audioFeedback.provideStateFeedback('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const cycleMode = () => {
    setMode(current => {
      switch (current) {
        case 'AUTO': return 'ICU';
        case 'ICU': return 'OPEN';
        case 'OPEN': return 'AUTO';
        default: return 'AUTO';
      }
    });
  };

  if (!permission) {
    return <View style={styles.container}><Text>Requesting camera permission...</Text></View>;
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>We need your permission to show the camera</Text>
        <TouchableOpacity style={styles.button} onPress={requestPermission}>
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.settingsButton} onPress={() => setShowSettings(true)}>
          <Text style={styles.settingsText}>⚙️</Text>
        </TouchableOpacity>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Read My Lips</Text>
          <Text style={styles.durationText}>{recordingDuration}s</Text>
        </View>
        <TouchableOpacity style={styles.modeButton} onPress={cycleMode}>
          <Text style={styles.modeText}>{mode}</Text>
        </TouchableOpacity>
      </View>

      {/* Camera View */}
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
          mode="video"
        >
          {/* Oval Guide */}
          <View style={styles.ovalGuide}>
            <View style={styles.oval} />
            <Text style={styles.guideText}>Position your face in the oval</Text>
          </View>

          {/* Recording Indicator */}
          {isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording {recordingDuration}s</Text>
            </View>
          )}

          {/* Recording Progress Bar */}
          {isRecording && (
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${recordingProgress}%` }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {Math.round(recordingProgress)}%
              </Text>
            </View>
          )}

          {/* Countdown */}
          {countdown !== null && (
            <View style={styles.countdownContainer}>
              <Text style={styles.countdownText}>{countdown}</Text>
            </View>
          )}

          {/* Processing Indicator */}
          {isProcessing && (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color="#fff" />
              <Text style={styles.processingText}>Processing...</Text>
            </View>
          )}
        </CameraView>
      </View>

      {/* Results Display */}
      <View style={styles.resultsContainer}>
        <Animated.Text style={[styles.connectionStatus, { opacity: fadeAnim }]}>
          {connectionStatus}
        </Animated.Text>
        <Text style={styles.resultLabel}>Last Prediction:</Text>
        <Text style={styles.resultText}>{lastPrediction || 'No prediction yet'}</Text>
        <Text style={styles.confidenceText}>
          Confidence: {(confidence * 100).toFixed(1)}%
        </Text>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton} onPress={toggleCameraFacing}>
          <Text style={styles.controlButtonText}>Flip</Text>
        </TouchableOpacity>
        
        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
          <TouchableOpacity
            style={[styles.recordButton, isRecording && styles.recordButtonActive]}
            onPress={startRecording}
            disabled={isRecording || isProcessing}
          >
            <View style={[styles.recordButtonInner, isRecording && styles.recordButtonInnerActive]} />
          </TouchableOpacity>
        </Animated.View>
        
        <TouchableOpacity style={styles.controlButton} onPress={cycleMode}>
          <Text style={styles.controlButtonText}>Mode</Text>
        </TouchableOpacity>
      </View>

      {/* Settings Modal */}
      <SettingsModal
        visible={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={(settings) => {
          console.log('Settings updated:', settings);
          // Update recording duration
          if (settings.recording?.duration) {
            setRecordingDuration(settings.recording.duration);
          }
          // Settings are automatically applied via the audioFeedback service
        }}
      />

      {/* Loading Screen */}
      <LoadingScreen
        visible={isProcessing}
        message="Processing video..."
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F1E8', // SMHS beige background
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#6B46C1', // SMHS purple
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  settingsButton: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  settingsText: {
    fontSize: 20,
    color: '#fff',
  },
  titleContainer: {
    alignItems: 'center',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  durationText: {
    color: '#F5F1E8', // Light beige
    fontSize: 12,
    marginTop: 2,
  },
  modeButton: {
    backgroundColor: '#DC2626', // SMHS red accent
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  modeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: '#6B46C1',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    borderWidth: 3,
    borderColor: '#6B46C1', // SMHS purple border
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  ovalGuide: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -170 }, { translateY: -120 }],
    alignItems: 'center',
  },
  oval: {
    width: 340,
    height: 240,
    borderRadius: 170,
    borderWidth: 4,
    borderColor: 'rgba(245, 241, 232, 0.8)', // SMHS beige oval
    shadowColor: '#F5F1E8',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.6,
    shadowRadius: 15,
    elevation: 8,
  },
  guideText: {
    color: '#F5F1E8', // SMHS beige text
    fontSize: 14,
    marginTop: 15,
    textAlign: 'center',
    backgroundColor: 'rgba(107, 70, 193, 0.8)', // SMHS purple background
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    fontWeight: '600',
  },
  recordingIndicator: {
    position: 'absolute',
    top: 20,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#DC2626', // SMHS red
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F5F1E8', // SMHS beige
    marginRight: 6,
  },
  recordingText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 12,
    fontWeight: 'bold',
  },
  progressContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(107, 70, 193, 0.3)', // SMHS purple with transparency
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6B46C1', // SMHS purple
    borderRadius: 3,
  },
  progressText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 12,
    marginTop: 5,
    fontWeight: 'bold',
  },
  countdownContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -35 }, { translateY: -35 }],
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(107, 70, 193, 0.9)', // SMHS purple
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#F5F1E8', // SMHS beige border
  },
  countdownText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 28,
    fontWeight: 'bold',
  },
  processingContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -60 }, { translateY: -60 }],
    alignItems: 'center',
    backgroundColor: 'rgba(107, 70, 193, 0.95)', // SMHS purple
    padding: 25,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#F5F1E8', // SMHS beige border
  },
  processingText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 16,
    marginTop: 10,
    fontWeight: '600',
  },
  resultsContainer: {
    backgroundColor: '#6B46C1', // SMHS purple
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  connectionStatus: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 12,
    marginBottom: 10,
    textAlign: 'center',
    fontWeight: '600',
  },
  resultLabel: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 14,
    marginBottom: 5,
    fontWeight: '600',
  },
  resultText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  confidenceText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 14,
    fontWeight: '600',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
    backgroundColor: '#6B46C1', // SMHS purple
  },
  controlButton: {
    backgroundColor: '#F5F1E8', // SMHS beige
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  controlButtonText: {
    color: '#6B46C1', // SMHS purple
    fontSize: 16,
    fontWeight: 'bold',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F5F1E8', // SMHS beige
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    borderWidth: 4,
    borderColor: '#6B46C1', // SMHS purple border
  },
  recordButtonActive: {
    backgroundColor: '#DC2626', // SMHS red when recording
    borderColor: '#F5F1E8', // SMHS beige border when recording
  },
  recordButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#6B46C1', // SMHS purple
  },
  recordButtonInnerActive: {
    width: 30,
    height: 30,
    borderRadius: 5,
    backgroundColor: '#F5F1E8', // SMHS beige
  },
  message: {
    textAlign: 'center',
    paddingBottom: 10,
    color: '#6B46C1', // SMHS purple
    fontWeight: '600',
  },
  button: {
    backgroundColor: '#6B46C1', // SMHS purple
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 10,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: '#F5F1E8', // SMHS beige
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LiveLipReader;
