import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Switch,
} from 'react-native';
import Slider from '@react-native-community/slider';
import CONFIG from '../config';
import audioFeedback from '../services/audioFeedback';

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
  onSettingsChange: (settings: any) => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  visible,
  onClose,
  onSettingsChange,
}) => {
  const [enableTTS, setEnableTTS] = useState(true);
  const [enableSounds, setEnableSounds] = useState(true);
  const [speechRate, setSpeechRate] = useState(CONFIG.SPEECH.RATE);
  const [speechPitch, setSpeechPitch] = useState(CONFIG.SPEECH.PITCH);
  const [icuThreshold, setIcuThreshold] = useState(CONFIG.CONFIDENCE_THRESHOLDS.ICU);
  const [openThreshold, setOpenThreshold] = useState(CONFIG.CONFIDENCE_THRESHOLDS.OPEN);
  const [recordingDuration, setRecordingDuration] = useState(CONFIG.RECORDING.DURATION);

  const handleSave = () => {
    const newSettings = {
      audio: {
        enableTTS,
        enableSounds,
        rate: speechRate,
        pitch: speechPitch,
      },
      confidence: {
        icu: icuThreshold,
        open: openThreshold,
      },
      recording: {
        duration: recordingDuration,
      },
    };

    // Update audio feedback service
    audioFeedback.updateOptions({
      enableTTS,
      enableSounds,
      rate: speechRate,
      pitch: speechPitch,
      volume: 1.0,
    });

    onSettingsChange(newSettings);
    onClose();
  };

  const handleReset = () => {
    setEnableTTS(true);
    setEnableSounds(true);
    setSpeechRate(CONFIG.SPEECH.RATE);
    setSpeechPitch(CONFIG.SPEECH.PITCH);
    setIcuThreshold(CONFIG.CONFIDENCE_THRESHOLDS.ICU);
    setOpenThreshold(CONFIG.CONFIDENCE_THRESHOLDS.OPEN);
    setRecordingDuration(CONFIG.RECORDING.DURATION);
  };

  const testSpeech = async () => {
    await audioFeedback.speak('This is a test of the speech settings', {
      rate: speechRate,
      pitch: speechPitch,
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Settings</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveButton}>Save</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          {/* Audio Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Audio Settings</Text>
            
            <View style={styles.setting}>
              <Text style={styles.settingLabel}>Enable Text-to-Speech</Text>
              <Switch
                value={enableTTS}
                onValueChange={setEnableTTS}
                trackColor={{ false: '#767577', true: '#007AFF' }}
                thumbColor={enableTTS ? '#fff' : '#f4f3f4'}
              />
            </View>

            <View style={styles.setting}>
              <Text style={styles.settingLabel}>Enable Sound Effects</Text>
              <Switch
                value={enableSounds}
                onValueChange={setEnableSounds}
                trackColor={{ false: '#767577', true: '#007AFF' }}
                thumbColor={enableSounds ? '#fff' : '#f4f3f4'}
              />
            </View>

            <View style={styles.sliderSetting}>
              <Text style={styles.settingLabel}>
                Speech Rate: {speechRate.toFixed(1)}
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.5}
                maximumValue={1.5}
                value={speechRate}
                onValueChange={setSpeechRate}
                minimumTrackTintColor="#007AFF"
                maximumTrackTintColor="#000000"
                thumbTintColor="#007AFF"
              />
            </View>

            <View style={styles.sliderSetting}>
              <Text style={styles.settingLabel}>
                Speech Pitch: {speechPitch.toFixed(1)}
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.5}
                maximumValue={1.5}
                value={speechPitch}
                onValueChange={setSpeechPitch}
                minimumTrackTintColor="#007AFF"
                maximumTrackTintColor="#000000"
                thumbTintColor="#007AFF"
              />
            </View>

            <TouchableOpacity style={styles.testButton} onPress={testSpeech}>
              <Text style={styles.testButtonText}>Test Speech</Text>
            </TouchableOpacity>
          </View>

          {/* Recognition Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recognition Settings</Text>
            
            <View style={styles.sliderSetting}>
              <Text style={styles.settingLabel}>
                ICU Confidence Threshold: {(icuThreshold * 100).toFixed(0)}%
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.4}
                maximumValue={0.9}
                value={icuThreshold}
                onValueChange={setIcuThreshold}
                minimumTrackTintColor="#007AFF"
                maximumTrackTintColor="#000000"
                thumbTintColor="#007AFF"
              />
            </View>

            <View style={styles.sliderSetting}>
              <Text style={styles.settingLabel}>
                Open Vocabulary Threshold: {(openThreshold * 100).toFixed(0)}%
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.3}
                maximumValue={0.8}
                value={openThreshold}
                onValueChange={setOpenThreshold}
                minimumTrackTintColor="#007AFF"
                maximumTrackTintColor="#000000"
                thumbTintColor="#007AFF"
              />
            </View>

            <View style={styles.sliderSetting}>
              <Text style={styles.settingLabel}>
                Recording Duration: {recordingDuration.toFixed(1)}s (Max: 8s)
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.5}
                maximumValue={8.0}
                value={recordingDuration}
                onValueChange={setRecordingDuration}
                minimumTrackTintColor="#007AFF"
                maximumTrackTintColor="#000000"
                thumbTintColor="#007AFF"
              />
              <View style={styles.sliderLabels}>
                <Text style={styles.sliderLabel}>0.5s</Text>
                <Text style={styles.sliderLabel}>8.0s</Text>
              </View>
            </View>
          </View>

          {/* Reset Button */}
          <TouchableOpacity style={styles.resetButton} onPress={handleReset}>
            <Text style={styles.resetButtonText}>Reset to Defaults</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  cancelButton: {
    color: '#007AFF',
    fontSize: 16,
  },
  saveButton: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  setting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  settingLabel: {
    color: '#fff',
    fontSize: 14,
  },
  sliderSetting: {
    paddingVertical: 10,
  },
  slider: {
    width: '100%',
    height: 40,
    marginTop: 5,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  sliderLabel: {
    color: '#888',
    fontSize: 12,
  },
  testButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignSelf: 'flex-start',
    marginTop: 10,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  resetButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignSelf: 'center',
    marginTop: 20,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SettingsModal;
