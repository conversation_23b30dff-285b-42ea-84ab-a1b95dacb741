// End-to-End Integration Test for ICU Lipreading App
// Tests the complete pipeline from mobile app to backend server

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://***********:8000';

// Test configuration
const TEST_CONFIG = {
  maxVideoSize: 50 * 1024 * 1024, // 50MB max
  supportedFormats: ['.mp4', '.mov', '.avi'],
  maxDuration: 8, // seconds
  minDuration: 0.5, // seconds
  confidenceThresholds: {
    ICU: 0.65,
    OPEN: 0.55
  }
};

class IntegrationTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(name, testFn) {
    console.log(`\n🧪 Running: ${name}`);
    try {
      const result = await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASS', result });
      console.log(`   ✅ PASS: ${name}`);
      return result;
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAIL', error: error.message });
      console.log(`   ❌ FAIL: ${name} - ${error.message}`);
      throw error;
    }
  }

  async testServerHealth() {
    return this.runTest('Server Health Check', async () => {
      const response = await fetch(`${API_BASE_URL}/health`);
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }
      const data = await response.json();
      if (data.status !== 'healthy') {
        throw new Error(`Server not healthy: ${data.status}`);
      }
      return data;
    });
  }

  async testCTCHealth() {
    return this.runTest('CTC Models Health Check', async () => {
      const response = await fetch(`${API_BASE_URL}/ctc/health`);
      if (!response.ok) {
        throw new Error(`CTC health check failed: ${response.status}`);
      }
      const data = await response.json();
      return data;
    });
  }

  async testCORSHeaders() {
    return this.runTest('CORS Headers Validation', async () => {
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:8081',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });
      
      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers')
      };
      
      if (!corsHeaders['access-control-allow-origin']) {
        throw new Error('CORS not properly configured');
      }
      
      return corsHeaders;
    });
  }

  async testVideoUploadFormat() {
    return this.runTest('Video Upload Format Validation', async () => {
      // Create a minimal valid video file simulation
      const mockVideoData = Buffer.alloc(1024, 0); // 1KB mock video
      
      const FormData = require('form-data');
      const form = new FormData();
      form.append('file', mockVideoData, {
        filename: 'test_video.mp4',
        contentType: 'video/mp4'
      });

      const response = await fetch(`${API_BASE_URL}/ctc/predict`, {
        method: 'POST',
        body: form,
        headers: form.getHeaders()
      });

      // We expect this to fail with a specific error (not a format error)
      const data = await response.json();
      
      // Check that it's not rejecting the format itself
      if (response.status === 415) {
        throw new Error('Server rejecting video format');
      }
      
      return { status: response.status, data };
    });
  }

  async testAllEndpoints() {
    const endpoints = [
      { path: '/ctc/predict', name: 'CTC Auto Mode' },
      { path: '/ctc/predict_icu', name: 'CTC ICU Mode' },
      { path: '/ctc/predict_open', name: 'CTC Open Mode' },
      { path: '/predict', name: 'Legacy Mode' }
    ];

    for (const endpoint of endpoints) {
      await this.runTest(`Endpoint: ${endpoint.name}`, async () => {
        const FormData = require('form-data');
        const form = new FormData();
        form.append('file', Buffer.alloc(512, 0), {
          filename: 'test.mp4',
          contentType: 'video/mp4'
        });

        const response = await fetch(`${API_BASE_URL}${endpoint.path}`, {
          method: 'POST',
          body: form,
          headers: form.getHeaders()
        });

        const data = await response.json();
        
        // Verify response structure
        if (!data.hasOwnProperty('success') && !data.hasOwnProperty('mode')) {
          throw new Error('Invalid response structure');
        }

        return { status: response.status, endpoint: endpoint.path, data };
      });
    }
  }

  async testResponseFormat() {
    return this.runTest('Response Format Validation', async () => {
      const FormData = require('form-data');
      const form = new FormData();
      form.append('file', Buffer.alloc(512, 0), {
        filename: 'test.mp4',
        contentType: 'video/mp4'
      });

      const response = await fetch(`${API_BASE_URL}/ctc/predict`, {
        method: 'POST',
        body: form,
        headers: form.getHeaders()
      });

      const data = await response.json();
      
      // Check required fields
      const requiredFields = ['mode', 'confidence'];
      const missingFields = requiredFields.filter(field => !(field in data));
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Validate confidence is a number between 0 and 1
      if (typeof data.confidence !== 'number' || data.confidence < 0 || data.confidence > 1) {
        throw new Error(`Invalid confidence value: ${data.confidence}`);
      }

      return data;
    });
  }

  async testNetworkLatency() {
    return this.runTest('Network Latency Test', async () => {
      const startTime = Date.now();
      
      const response = await fetch(`${API_BASE_URL}/health`);
      await response.json();
      
      const latency = Date.now() - startTime;
      
      if (latency > 5000) { // 5 seconds
        throw new Error(`High latency detected: ${latency}ms`);
      }

      return { latency: `${latency}ms` };
    });
  }

  async testErrorHandling() {
    return this.runTest('Error Handling Validation', async () => {
      // Test with invalid endpoint
      const response = await fetch(`${API_BASE_URL}/invalid-endpoint`);
      
      if (response.status !== 404) {
        throw new Error(`Expected 404 for invalid endpoint, got ${response.status}`);
      }

      // Test with invalid method
      const response2 = await fetch(`${API_BASE_URL}/health`, { method: 'DELETE' });
      
      if (response2.status !== 405 && response2.status !== 404) {
        throw new Error(`Expected 405/404 for invalid method, got ${response2.status}`);
      }

      return { invalidEndpoint: response.status, invalidMethod: response2.status };
    });
  }

  async runAllTests() {
    console.log('🚀 Starting End-to-End Integration Tests');
    console.log(`Server: ${API_BASE_URL}`);
    console.log('=' .repeat(60));

    try {
      // Core functionality tests
      await this.testServerHealth();
      await this.testCTCHealth();
      await this.testCORSHeaders();
      
      // API integration tests
      await this.testVideoUploadFormat();
      await this.testAllEndpoints();
      await this.testResponseFormat();
      
      // Performance and reliability tests
      await this.testNetworkLatency();
      await this.testErrorHandling();

    } catch (error) {
      console.log(`\n⚠️  Test suite encountered errors, but continuing...`);
    }

    // Print summary
    this.printSummary();
    
    return this.results;
  }

  printSummary() {
    console.log('\n' + '=' .repeat(60));
    console.log('📊 Integration Test Summary');
    console.log('=' .repeat(60));
    
    const total = this.results.passed + this.results.failed;
    const passRate = total > 0 ? ((this.results.passed / total) * 100).toFixed(1) : 0;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${this.results.passed} ✅`);
    console.log(`Failed: ${this.results.failed} ❌`);
    console.log(`Pass Rate: ${passRate}%`);
    
    console.log('\n📋 Test Details:');
    this.results.tests.forEach((test, index) => {
      const status = test.status === 'PASS' ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${test.name}`);
      if (test.status === 'FAIL') {
        console.log(`   Error: ${test.error}`);
      }
    });

    console.log('\n🎯 Integration Status:');
    if (this.results.failed === 0) {
      console.log('🎉 All integration tests passed! App is ready for demo.');
    } else if (this.results.passed > this.results.failed) {
      console.log('⚠️  Most tests passed. App is functional with minor issues.');
    } else {
      console.log('❌ Multiple test failures. Review integration before demo.');
    }

    console.log('\n📱 Next Steps:');
    console.log('1. Start Expo development server: npx expo start');
    console.log('2. Scan QR code with Expo Go app');
    console.log('3. Test camera recording and video processing');
    console.log('4. Verify audio feedback and mode switching');
    console.log('5. Test 8-second recording capability');
  }
}

// Add FormData polyfill for Node.js
if (typeof FormData === 'undefined') {
  global.FormData = require('form-data');
}

// Run the integration tests
const tester = new IntegrationTester();
tester.runAllTests().then(results => {
  const success = results.failed === 0 || results.passed > results.failed;
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Integration test suite failed:', error);
  process.exit(1);
});
