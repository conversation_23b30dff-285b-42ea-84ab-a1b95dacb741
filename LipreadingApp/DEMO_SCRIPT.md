# ICU Lipreading App - Demo Script

## 🎬 Presentation Overview

**Duration**: 10-15 minutes  
**Audience**: Technical stakeholders, medical professionals, investors  
**Goal**: Demonstrate complete full-stack lipreading solution for ICU environments

## 📋 Pre-Demo Checklist

### Technical Setup (5 minutes before demo)
- [ ] Backend server running: `export VSR_IMPL=ctc && python -m backend.api.app`
- [ ] Expo server running: `npx expo start`
- [ ] QR code visible and ready to scan
- [ ] Phone connected to same Wi-Fi network
- [ ] Expo Go app installed on demo device
- [ ] Server health check passed: `curl http://***********:8000/health`
- [ ] Audio/video permissions granted
- [ ] Demo environment lighting adequate for camera

### Backup Plans
- [ ] Screenshots/video recording of app in action
- [ ] Alternative demo device ready
- [ ] Hotspot backup for network issues
- [ ] Presentation slides with key features

## 🎯 Demo Flow

### 1. Introduction (2 minutes)

**Opening Statement:**
> "Today I'll demonstrate a complete, functioning lipreading application designed specifically for ICU environments. This is a full-stack solution with a mobile app that can record video, process it through our backend AI models, and provide immediate audio feedback."

**Key Points to Highlight:**
- Real-time lipreading for ICU patients who cannot speak
- Mobile-first approach for accessibility
- Multiple recognition modes for different scenarios
- Production-ready prototype with 8-second video capability

### 2. Architecture Overview (2 minutes)

**Show the Technical Stack:**
- **Frontend**: Expo React Native app (iOS/Android compatible)
- **Backend**: FastAPI server with CTC-based lipreading models
- **Integration**: Real-time video processing pipeline
- **Deployment**: LAN-based for hospital environments

**Demonstrate Server Status:**
```bash
# Show server running
curl http://***********:8000/health

# Show CTC models status
curl http://***********:8000/ctc/health
```

### 3. Mobile App Demo (8 minutes)

#### 3.1 App Installation (1 minute)
1. **Show QR Code**: Display the Expo QR code
2. **Scan with Phone**: Use Expo Go to scan and load app
3. **App Launch**: Show professional loading screen and interface

**Script:**
> "The app installs instantly through Expo Go - no app store approval needed. This allows for rapid deployment and updates in hospital environments."

#### 3.2 Core Interface Tour (2 minutes)
1. **Header Elements**:
   - Settings button (⚙️)
   - App title "ICU Lipreading"
   - Current recording duration (3s)
   - Mode indicator (AUTO/ICU/OPEN)

2. **Camera Interface**:
   - Front-facing camera preview
   - Blue oval guide for face positioning
   - Professional UI with shadows and animations

3. **Status Display**:
   - Connection status (green = connected)
   - Last prediction results
   - Confidence percentage

4. **Controls**:
   - Camera flip button
   - Large record button with animation
   - Mode switching button

**Script:**
> "The interface is designed for ease of use in medical environments - large buttons, clear visual feedback, and intuitive controls."

#### 3.3 Recording Demonstration (3 minutes)
1. **Position Face**: Show oval guide alignment
2. **Start Recording**: 
   - Tap record button
   - Show 3-second countdown
   - Demonstrate recording progress bar
   - Show pulsing animation during recording

3. **Processing Phase**:
   - Show loading screen with spinner
   - Explain backend processing
   - Display response time

4. **Results Display**:
   - Show prediction text
   - Display confidence percentage
   - Demonstrate audio feedback (TTS)

**Script:**
> "The app records video, sends it to our backend for processing, and provides immediate audio feedback. The progress bar keeps users informed throughout the process."

#### 3.4 Mode Switching Demo (1 minute)
1. **AUTO Mode**: Explain intelligent mode selection
2. **ICU Mode**: Show medical vocabulary focus (65% threshold)
3. **OPEN Mode**: Demonstrate general vocabulary (55% threshold)

**Script:**
> "Different modes optimize for different scenarios - ICU mode for medical terminology with higher accuracy requirements, OPEN mode for general conversation."

#### 3.5 Settings Configuration (1 minute)
1. **Open Settings**: Tap ⚙️ button
2. **Recording Duration**: Adjust from 3s to 8s maximum
3. **Audio Settings**: Modify speech rate and pitch
4. **Confidence Thresholds**: Show customizable accuracy levels
5. **Test Speech**: Demonstrate TTS functionality

**Script:**
> "The app is highly configurable - recording duration up to 8 seconds, adjustable confidence thresholds, and customizable audio feedback to meet different patient needs."

### 4. Technical Deep Dive (2 minutes)

#### 4.1 Backend Processing
**Show Server Logs:**
- Real-time request processing
- Response times and status codes
- Model inference details

#### 4.2 Network Architecture
**Explain:**
- LAN-based deployment for security
- CORS configuration for mobile access
- RESTful API design
- Health monitoring endpoints

#### 4.3 Performance Metrics
**Highlight:**
- Sub-second app launch
- 2-5 second processing times
- Real-time progress feedback
- Robust error handling

**Script:**
> "The system is designed for production use with enterprise-grade performance, security, and reliability."

### 5. Advanced Features Demo (1 minute)

#### 5.1 Error Handling
1. **Network Issues**: Show graceful degradation
2. **Low Confidence**: Demonstrate "Please repeat" feedback
3. **Server Offline**: Show connection status updates

#### 5.2 Accessibility Features
1. **Visual Feedback**: Progress bars, animations, status indicators
2. **Audio Feedback**: Immediate TTS responses
3. **Large UI Elements**: Easy interaction for medical staff

**Script:**
> "The app handles real-world scenarios gracefully with comprehensive error handling and accessibility features."

## 🎯 Key Talking Points

### Technical Excellence
- "Full-stack solution with modern architecture"
- "Production-ready with comprehensive testing"
- "8-second video capability with real-time progress"
- "Cross-platform mobile deployment"

### Medical Relevance
- "Designed specifically for ICU environments"
- "Multiple recognition modes for different scenarios"
- "Immediate audio feedback for patient communication"
- "Configurable accuracy thresholds for medical safety"

### Business Value
- "Rapid deployment through Expo Go"
- "No app store dependencies"
- "Scalable architecture for hospital networks"
- "Customizable for different medical specialties"

## 🔧 Troubleshooting During Demo

### If App Won't Load
1. Check Wi-Fi connection
2. Restart Expo Go app
3. Re-scan QR code
4. Use backup device

### If Camera Issues
1. Check permissions in iOS Settings
2. Restart app
3. Try different lighting
4. Use pre-recorded demo video

### If Server Issues
1. Check server logs
2. Restart backend server
3. Verify network connectivity
4. Fall back to presentation slides

### If Audio Issues
1. Check device volume
2. Disable silent mode
3. Test in settings panel
4. Use visual feedback only

## 📊 Success Metrics to Highlight

### Technical Achievements
- ✅ 100% functional mobile app
- ✅ Complete backend integration
- ✅ 8-second video recording capability
- ✅ Real-time processing pipeline
- ✅ Professional UI/UX design

### Demo Readiness
- ✅ QR code installation
- ✅ Network connectivity verified
- ✅ All endpoints tested
- ✅ Error handling demonstrated
- ✅ Settings customization shown

## 🎬 Closing Statement

> "This demonstrates a complete, production-ready lipreading solution that can be deployed immediately in ICU environments. The combination of mobile accessibility, real-time processing, and medical-specific features makes this a practical tool for improving patient communication in critical care settings."

**Next Steps:**
1. Model training with full medical vocabulary
2. Clinical trials and validation
3. Integration with hospital systems
4. Deployment to app stores

---

**Demo Duration**: 10-15 minutes  
**Status**: Ready for presentation ✅
